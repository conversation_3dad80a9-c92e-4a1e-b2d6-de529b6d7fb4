# Architecture

## Overview

Tahimoto employs a microservice architecture to ensure scalability, maintainability, and flexibility. The key components are:

1.  **Frontend:**  A Vue.js application built with TypeScript, Vite, and Tailwind CSS, providing the user interface.
2.  **Backend (API):**  A FastAPI application serving a GraphQL API using Ariadne. This layer handles requests from the frontend, interacts with the Agent Service, and queries the Neo4j database.
3.  **Agent Service:**  A separate FastAPI application responsible for thematic analysis, reasoning, and real-time learning. It uses an LLM (with SSM/Transformers for memory) and interacts with the Neo4j database.
4.  **Database (Neo4j):** A graph database storing story data, themes, and relationships.
5.  **Cache (Redis):**  Used for caching API responses and frequently accessed data.

## Component Responsibilities

### Frontend (Vue + TypeScript)

-   Provides the user interface for searching, browsing, and viewing stories.
-   Built with Vue 3, Vite, and Tailwind CSS for modern development experience.
-   Communicates with the Backend (API) via GraphQL queries.
-   Handles user interactions and displays data.
-   Uses <PERSON><PERSON> for state management and Vue Router for navigation.

### Backend (FastAPI + Ariadne)

-   Exposes a GraphQL API using Ariadne.
-   Handles incoming GraphQL requests from the frontend.
-   Validates requests using Pydantic models.
-   Defines GraphQL resolvers that:
    -   Fetch data from the Neo4j database.
    -   Communicate with the Agent Service for thematic analysis and recommendations.
-   Integrates with Redis for caching.
-   Houses the ThemeMapper interface and implementations, providing:
    -   Manual mapping functionality for immediate use
    -   Agent-ready interfaces for future integration
    -   A factory pattern for switching between implementations

### Agent Service (FastAPI)

-   Performs in-depth thematic analysis of story data (descriptions, reviews, tags).
-   Uses an LLM (with SSM/Transformers for persistent memory) for reasoning and relationship discovery.
-   Makes requests to external data sources (AniList, MAL, etc.) as needed (using its "tool" capabilities).
-   Interacts with the Neo4j database to store and retrieve thematic information.
-   Exposes API endpoints for:
    -   Analyzing stories.
    -   Getting recommendations.
    -   Updating its knowledge base.
-   Implements the agent-side of the ThemeMapper interface, providing sophisticated theme extraction capabilities.

### Database (Neo4j)

-   Stores story data, themes, and relationships in a graph structure.
-   Nodes represent entities (stories, themes, genres, users, etc.).
-   Edges represent relationships between entities (e.g., `HAS_THEME`, `SIMILAR_TO`, `BELONGS_TO_GENRE`).
-   Provides efficient querying of relationships using Cypher.

### Cache (Redis)

-   Caches API responses (from the Backend).
-   Caches frequently accessed data (e.g., theme mappings).
-   Improves performance and reduces database load.

## Data Flow

1.  A user interacts with the Frontend, initiating a search or requesting a recommendation.
2.  The Frontend sends a GraphQL query to the Backend (API).
3.  The Backend (Ariadne) receives the query and calls the appropriate resolver.
4.  The resolver:
    *   May query the Neo4j database directly for basic data retrieval.
    *   May use the ThemeMapper interface to map media-specific metadata to universal themes.
    *   May make an API call to the Agent Service for thematic analysis or recommendations.
5.  The Agent Service:
    *   Performs the requested analysis or reasoning using its LLM and persistent memory.
    *   May query the Neo4j database for additional information.
    *   Returns the results to the Backend.
6.  The Backend (resolver) formats the data and returns it to Ariadne.
7.  Ariadne sends the GraphQL response back to the Frontend.
8.  The Frontend displays the results to the user.

## Thematic Mapping Flow

Tahimoto implements a flexible thematic mapping system that can operate with or without the Agent Service:

1. The Backend contains a ThemeMapper interface with multiple implementations:
   * A manual mapper that uses predefined rules and mappings
   * An agent-ready mapper that can communicate with the Agent Service
   
2. For each media type (anime, books, movies), there are specific mapper implementations.

3. A configuration setting determines whether to use the manual mapper or the agent mapper.

4. When agent-based mapping is enabled:
   * The Backend calls the Agent Service with media metadata
   * The Agent Service performs advanced thematic analysis
   * Results are returned to the Backend and cached

5. When manual mapping is used (or as a fallback):
   * The Backend uses predefined mappings to extract themes
   * No external service call is needed
   * Results are still cached for performance

See [Thematic Mapping Process](Thematic%20Mapping%20Process.md) for more details on the mapping interface and implementation.

## Neo4j Database Workflow

Tahimoto uses Neo4j as its graph database to efficiently store and query thematic relationships. The database workflow is organized as follows:

### 1. Database Session Management

- Neo4j sessions are managed through a dedicated module (`neo4j_session.py`)
- Sessions are provided to FastAPI endpoints and GraphQL resolvers via dependency injection
- Each resolver receives a database session through the request context
- Sessions are automatically closed after each request

### 2. CRUD Operations

The database interaction follows a clean separation of concerns:

- **GraphQL Resolvers**: Handle GraphQL queries and mutations, using the injected Neo4j session
- **CRUD Operations**: Implement database queries using Cypher, organized by entity type (themes, stories)
- **Cypher Queries**: Used for all database operations, optimized for graph traversal

The flow for a typical operation:

1. GraphQL query/mutation received by Ariadne
2. Resolver function called with context containing Neo4j session
3. Resolver calls appropriate CRUD function
4. CRUD function executes Cypher query using the session
5. Results are transformed to match the GraphQL schema
6. Data is returned through the resolver to the client

### 3. Graph Data Model

The Neo4j graph model provides several advantages for Tahimoto's theme-based recommendations:

- **Nodes** represent entities:
  - Stories (anime, books, movies)
  - Themes
  - Genres
  - Tags

- **Relationships** represent connections between entities:
  - HAS_THEME: Connects a story to a theme
  - BELONGS_TO_GENRE: Connects a story to a genre
  - RELATED_TO: Connects themes to each other
  - SIMILAR_TO: Connects similar stories

- **Properties** on nodes and relationships:
  - Nodes: Metadata about the entity (title, description, etc.)
  - Relationships: Strength of the connection, source of the mapping (manual/agent)

### 4. Query Patterns

Tahimoto uses specific query patterns for common operations:

- **Theme Lookup**: Finding themes for a specific story
```cypher
MATCH (s:Story {id: $story_id})-[r:HAS_THEME]->(t:Theme)
RETURN t, r.strength AS strength, r.mapping_type AS type
```

- **Cross-Media Recommendations**: Finding similar stories across media types
```cypher
MATCH (s1:Story {id: $story_id})-[r1:HAS_THEME]->(t:Theme)<-[r2:HAS_THEME]-(s2:Story)
WHERE s1.media_type <> s2.media_type
RETURN s2, sum(r1.strength * r2.strength) AS similarity
ORDER BY similarity DESC
LIMIT 10
```

- **Theme Network Analysis**: Understanding theme relationships
```cypher
MATCH (t1:Theme)<-[:HAS_THEME]-(s:Story)-[:HAS_THEME]->(t2:Theme)
WHERE t1 <> t2
RETURN t1, t2, count(s) AS shared_stories
ORDER BY shared_stories DESC
```

## Communication

-   **Frontend <-> Backend:** GraphQL over HTTP.
-   **Backend <-> Agent Service:** HTTP requests (REST API) - could be replaced with a message queue (RabbitMQ, Kafka) for asynchronous processing in the future.
-   **Backend <-> Neo4j:** Neo4j Python driver.
-   **Agent Service <-> Neo4j:** Neo4j Python driver.

## Technology Stack

See [Technology Stack](Technology%20Stack%20update.md) for a detailed breakdown.