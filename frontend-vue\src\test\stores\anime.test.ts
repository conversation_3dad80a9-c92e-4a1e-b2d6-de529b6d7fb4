import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON>inia, createPinia } from 'pinia'
import { useAnimeStore } from '@/stores/anime'
import * as animeApi from '@/services/animeApi'

// Mock the anime API
vi.mock('@/services/animeApi', () => ({
  searchAnime: vi.fn(),
  getAnimeById: vi.fn(),
}))

describe('Anime Store', () => {
  let store: ReturnType<typeof useAnimeStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useAnimeStore()
    vi.clearAllMocks()
  })

  it('initializes with correct default state', () => {
    expect(store.searchResults).toEqual([])
    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
    expect(store.currentPage).toBe(1)
    expect(store.totalPages).toBe(1)
    expect(store.selectedAnime).toBe(null)
  })

  it('handles successful search', async () => {
    const mockResponse = {
      data: [
        {
          id: 1,
          title: { romaji: 'One Piece' },
          coverImage: { medium: 'test.jpg' },
          description: 'Test description',
          genres: ['Adventure'],
          averageScore: 90,
        },
      ],
      pagination: {
        currentPage: 1,
        totalPages: 5,
        hasNextPage: true,
        hasPreviousPage: false,
      },
    }

    vi.mocked(animeApi.searchAnime).mockResolvedValue(mockResponse)

    await store.searchAnime('One Piece', 1)

    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
    expect(store.searchResults).toEqual(mockResponse.data)
    expect(store.currentPage).toBe(1)
    expect(store.totalPages).toBe(5)
    expect(store.hasNextPage).toBe(true)
    expect(store.hasPreviousPage).toBe(false)
  })

  it('handles search error', async () => {
    const errorMessage = 'Network error'
    vi.mocked(animeApi.searchAnime).mockRejectedValue(new Error(errorMessage))

    await store.searchAnime('One Piece', 1)

    expect(store.loading).toBe(false)
    expect(store.error).toBe(errorMessage)
    expect(store.searchResults).toEqual([])
  })

  it('sets loading state during search', async () => {
    let resolvePromise: (value: any) => void
    const promise = new Promise((resolve) => {
      resolvePromise = resolve
    })
    
    vi.mocked(animeApi.searchAnime).mockReturnValue(promise)

    const searchPromise = store.searchAnime('One Piece', 1)
    
    expect(store.loading).toBe(true)
    
    resolvePromise!({
      data: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    })
    
    await searchPromise
    expect(store.loading).toBe(false)
  })

  it('handles anime detail fetch', async () => {
    const mockAnime = {
      id: 1,
      title: { romaji: 'One Piece' },
      coverImage: { medium: 'test.jpg' },
      description: 'Test description',
      genres: ['Adventure'],
      averageScore: 90,
    }

    vi.mocked(animeApi.getAnimeById).mockResolvedValue(mockAnime)

    await store.fetchAnimeById(1)

    expect(store.selectedAnime).toEqual(mockAnime)
    expect(store.error).toBe(null)
  })

  it('clears search results', () => {
    store.searchResults = [{ id: 1 } as any]
    store.clearSearch()
    expect(store.searchResults).toEqual([])
  })
})
