import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { animeApi } from '@/services/api'
import type {
  AnimeSearchQuery,
  AnimeSearchResponse,
  AnimeSearchResult,
} from '@/types/theme'

export const useAnimeStore = defineStore('anime', () => {
  // State
  const searchResults = ref<AnimeSearchResponse | null>(null)
  const currentAnime = ref<AnimeSearchResult | null>(null)
  const selectedAnime = ref<AnimeSearchResult | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const hasResults = computed(() => searchResults.value && searchResults.value.items.length > 0)
  const hasError = computed(() => error.value !== null)
  const totalResults = computed(() => searchResults.value?.total || 0)
  const currentPage = computed(() => searchResults.value?.page || 1)
  const totalPages = computed(() => searchResults.value?.total_pages || 1)

  // Actions
  async function searchAnime(query: AnimeSearchQuery) {
    try {
      loading.value = true
      error.value = null
      searchResults.value = await animeApi.searchAnime(query)
      return searchResults.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to search anime'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function getAnime(id: string) {
    try {
      loading.value = true
      error.value = null
      currentAnime.value = await animeApi.getAnime(id)
      return currentAnime.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get anime details'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function getAnimeMetadata(id: string) {
    try {
      loading.value = true
      error.value = null
      const metadata = await animeApi.getAnimeMetadata(id)
      return metadata
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get anime metadata'
      throw err
    } finally {
      loading.value = false
    }
  }

  function selectAnime(anime: AnimeSearchResult) {
    selectedAnime.value = anime
  }

  function clearSelection() {
    selectedAnime.value = null
  }

  function clearResults() {
    searchResults.value = null
  }

  function clearError() {
    error.value = null
  }

  function clearCurrentAnime() {
    currentAnime.value = null
  }

  return {
    // State
    searchResults,
    currentAnime,
    selectedAnime,
    loading,
    error,
    
    // Getters
    hasResults,
    hasError,
    totalResults,
    currentPage,
    totalPages,
    
    // Actions
    searchAnime,
    getAnime,
    getAnimeMetadata,
    selectAnime,
    clearSelection,
    clearResults,
    clearError,
    clearCurrentAnime,
  }
})
