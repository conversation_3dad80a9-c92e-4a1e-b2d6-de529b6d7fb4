import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import AnimeSearch from '@/components/AnimeSearch.vue'
import { useAnimeStore } from '@/stores/anime'

// Mock the anime store
vi.mock('@/stores/anime', () => ({
  useAnimeStore: vi.fn(),
}))

describe('AnimeSearch', () => {
  let wrapper: any
  let mockAnimeStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Mock store methods and state
    mockAnimeStore = {
      searchAnime: vi.fn(),
      searchResults: [],
      loading: false,
      error: null,
      currentPage: 1,
      totalPages: 1,
      hasNextPage: false,
      hasPreviousPage: false,
    }
    
    vi.mocked(useAnimeStore).mockReturnValue(mockAnimeStore)
  })

  it('renders search form correctly', () => {
    wrapper = mount(AnimeSearch)
    
    expect(wrapper.find('input[type="text"]').exists()).toBe(true)
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
  })

  it('calls searchAnime when form is submitted', async () => {
    wrapper = mount(AnimeSearch)
    
    const searchInput = wrapper.find('input[type="text"]')
    const searchForm = wrapper.find('form')
    
    await searchInput.setValue('One Piece')
    await searchForm.trigger('submit')
    
    expect(mockAnimeStore.searchAnime).toHaveBeenCalledWith('One Piece', 1)
  })

  it('displays loading state', async () => {
    mockAnimeStore.loading = true
    wrapper = mount(AnimeSearch)
    
    expect(wrapper.text()).toContain('Searching')
  })

  it('displays error message when error occurs', async () => {
    mockAnimeStore.error = 'Search failed'
    wrapper = mount(AnimeSearch)
    
    expect(wrapper.text()).toContain('Search failed')
  })

  it('displays search results', async () => {
    mockAnimeStore.searchResults = [
      {
        id: 1,
        title: { romaji: 'One Piece' },
        coverImage: { medium: 'test.jpg' },
        description: 'Test description',
        genres: ['Adventure'],
        averageScore: 90,
      },
    ]
    
    wrapper = mount(AnimeSearch)
    
    expect(wrapper.text()).toContain('One Piece')
    expect(wrapper.text()).toContain('Adventure')
  })

  it('handles pagination correctly', async () => {
    mockAnimeStore.hasNextPage = true
    mockAnimeStore.currentPage = 1
    
    wrapper = mount(AnimeSearch)
    
    const nextButton = wrapper.find('[data-testid="next-page"]')
    if (nextButton.exists()) {
      await nextButton.trigger('click')
      expect(mockAnimeStore.searchAnime).toHaveBeenCalled()
    }
  })
})
