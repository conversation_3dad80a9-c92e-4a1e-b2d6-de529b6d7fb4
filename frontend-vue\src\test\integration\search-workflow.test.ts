import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import AnimeDetail from '@/views/AnimeDetail.vue'
import * as animeApi from '@/services/animeApi'

// Mock the anime API
vi.mock('@/services/animeApi', () => ({
  searchAnime: vi.fn(),
  getAnimeById: vi.fn(),
}))

describe('Search to Detail Workflow', () => {
  let router: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', name: 'Home', component: Home },
        { path: '/anime/:id', name: 'AnimeDetail', component: AnimeDetail, props: true },
      ],
    })
    
    vi.clearAllMocks()
  })

  it('completes full search to detail workflow', async () => {
    // Mock search response
    const mockSearchResponse = {
      data: [
        {
          id: 21,
          title: { romaji: 'One Piece' },
          coverImage: { medium: 'onepiece.jpg' },
          description: 'Pirate adventure',
          genres: ['Adventure', 'Comedy'],
          averageScore: 90,
        },
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    }

    // Mock detail response
    const mockDetailResponse = {
      id: 21,
      title: { romaji: 'One Piece', english: 'One Piece' },
      coverImage: { medium: 'onepiece.jpg', large: 'onepiece_large.jpg' },
      bannerImage: 'onepiece_banner.jpg',
      description: 'Detailed pirate adventure story',
      genres: ['Adventure', 'Comedy', 'Drama'],
      averageScore: 90,
      episodes: 1000,
      status: 'RELEASING',
      startDate: { year: 1999, month: 10, day: 20 },
      studios: { nodes: [{ name: 'Toei Animation' }] },
      relations: { edges: [] },
    }

    vi.mocked(animeApi.searchAnime).mockResolvedValue(mockSearchResponse)
    vi.mocked(animeApi.getAnimeById).mockResolvedValue(mockDetailResponse)

    // Mount Home component
    const homeWrapper = mount(Home, {
      global: {
        plugins: [pinia, router],
      },
    })

    // Wait for component to mount
    await homeWrapper.vm.$nextTick()

    // Find search input and perform search
    const searchInput = homeWrapper.find('input[type="text"]')
    const searchForm = homeWrapper.find('form')

    expect(searchInput.exists()).toBe(true)
    
    await searchInput.setValue('One Piece')
    await searchForm.trigger('submit')

    // Wait for search to complete
    await new Promise(resolve => setTimeout(resolve, 100))

    // Verify search was called
    expect(animeApi.searchAnime).toHaveBeenCalledWith('One Piece', 1)

    // Simulate clicking on search result (navigate to detail)
    await router.push('/anime/21')
    await router.isReady()

    // Mount detail component
    const detailWrapper = mount(AnimeDetail, {
      props: { id: '21' },
      global: {
        plugins: [pinia, router],
      },
    })

    // Wait for detail to load
    await new Promise(resolve => setTimeout(resolve, 100))

    // Verify detail API was called
    expect(animeApi.getAnimeById).toHaveBeenCalledWith(21)

    // Verify detail component shows correct data
    expect(detailWrapper.text()).toContain('One Piece')
    expect(detailWrapper.text()).toContain('Adventure')
  })

  it('handles search errors gracefully', async () => {
    vi.mocked(animeApi.searchAnime).mockRejectedValue(new Error('Network error'))

    const wrapper = mount(Home, {
      global: {
        plugins: [pinia, router],
      },
    })

    const searchInput = wrapper.find('input[type="text"]')
    const searchForm = wrapper.find('form')

    await searchInput.setValue('Test')
    await searchForm.trigger('submit')

    // Wait for error to be handled
    await new Promise(resolve => setTimeout(resolve, 100))

    // Should show error message
    expect(wrapper.text()).toContain('error') || expect(wrapper.text()).toContain('Error')
  })

  it('handles detail loading errors gracefully', async () => {
    vi.mocked(animeApi.getAnimeById).mockRejectedValue(new Error('Anime not found'))

    await router.push('/anime/999')
    await router.isReady()

    const wrapper = mount(AnimeDetail, {
      props: { id: '999' },
      global: {
        plugins: [pinia, router],
      },
    })

    // Wait for error to be handled
    await new Promise(resolve => setTimeout(resolve, 100))

    // Should show error message
    expect(wrapper.text()).toContain('error') || expect(wrapper.text()).toContain('Error')
  })
})
