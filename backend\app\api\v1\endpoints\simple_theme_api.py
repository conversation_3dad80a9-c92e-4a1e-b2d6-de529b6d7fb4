"""
Simplified Theme API for MCP/API Architecture

Lightweight theme mapping endpoints that work without database dependencies.
Perfect for MCP server integration and rapid theme analysis.
"""
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.services.simple_theme_mapper import SimpleThemeMapper, ThemeMapping, ThemeCategory, MappingType

logger = logging.getLogger(__name__)

router = APIRouter()

# Request/Response Models
class AnimeMetadataRequest(BaseModel):
    """Request model for anime metadata."""
    title_romaji: Optional[str] = None
    title_english: Optional[str] = None
    genres: List[str] = []
    tags: List[Dict[str, Any]] = []
    synopsis: Optional[str] = None

class ThemeMappingResponse(BaseModel):
    """Response model for theme mappings."""
    theme_id: str
    theme_name: str
    theme_category: str
    mapping_strength: float = Field(ge=0.0, le=1.0)
    mapping_type: str
    source: str
    context: str

class ThemeDefinitionResponse(BaseModel):
    """Response model for theme definitions."""
    id: str
    name: str
    category: str
    description: str

class MappingStatsResponse(BaseModel):
    """Response model for mapping statistics."""
    total_themes_available: int
    mappable_genres: int
    mappable_tags: int
    theme_categories: int
    mapping_types: int

# Dependency
async def get_theme_mapper() -> SimpleThemeMapper:
    """Get theme mapper instance."""
    return SimpleThemeMapper()

@router.post("/anime/analyze", response_model=List[ThemeMappingResponse])
async def analyze_anime_themes(
    anime_metadata: AnimeMetadataRequest,
    mapper: SimpleThemeMapper = Depends(get_theme_mapper)
) -> List[ThemeMappingResponse]:
    """
    Analyze anime metadata and return theme mappings.
    
    This is a lightweight endpoint that computes themes on-demand
    without requiring database storage. Perfect for MCP integration.
    """
    try:
        # Convert to dict format
        metadata_dict = {
            "title_romaji": anime_metadata.title_romaji,
            "title_english": anime_metadata.title_english,
            "genres": anime_metadata.genres,
            "tags": anime_metadata.tags,
            "synopsis": anime_metadata.synopsis
        }
        
        # Get theme mappings
        mappings = await mapper.map_anime_to_themes(metadata_dict)
        
        # Convert to response format
        response_mappings = []
        for mapping in mappings:
            response_mappings.append(ThemeMappingResponse(
                theme_id=mapping.theme_id,
                theme_name=mapping.theme_name,
                theme_category=mapping.theme_category.value,
                mapping_strength=mapping.mapping_strength,
                mapping_type=mapping.mapping_type.value,
                source=mapping.source,
                context=mapping.context
            ))
        
        logger.info(f"Analyzed {len(response_mappings)} themes for anime: {anime_metadata.title_romaji}")
        return response_mappings
        
    except Exception as e:
        logger.error(f"Error analyzing anime themes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing themes: {str(e)}")

@router.get("/anime/{anime_id}/themes", response_model=List[ThemeMappingResponse])
async def get_anime_themes_by_id(
    anime_id: str,
    mapper: SimpleThemeMapper = Depends(get_theme_mapper)
) -> List[ThemeMappingResponse]:
    """
    Get themes for a specific anime by ID.
    
    This endpoint would typically fetch anime metadata from your database
    and then compute themes on-demand. For now, it returns a sample response
    to demonstrate the API structure.
    """
    try:
        # TODO: Fetch anime metadata from your database using anime_id
        # For now, return sample data for testing
        
        # Sample metadata for testing (you'd replace this with actual database lookup)
        if anime_id == "story_16498":  # NARUTO example
            sample_metadata = {
                "title_romaji": "Naruto",
                "title_english": "Naruto",
                "genres": ["Action", "Adventure", "Martial Arts", "Comedy", "Drama"],
                "tags": [
                    {"name": "Ninja", "rank": 95},
                    {"name": "Coming of Age", "rank": 85},
                    {"name": "Friendship", "rank": 90},
                    {"name": "Rivalry", "rank": 80},
                    {"name": "Character Development", "rank": 75}
                ],
                "synopsis": "Naruto follows the story of a young ninja..."
            }
        else:
            # For other anime IDs, return empty for now
            sample_metadata = {
                "title_romaji": f"Anime {anime_id}",
                "genres": ["Drama"],
                "tags": []
            }
        
        # Get theme mappings
        mappings = await mapper.map_anime_to_themes(sample_metadata)
        
        # Convert to response format
        response_mappings = []
        for mapping in mappings:
            response_mappings.append(ThemeMappingResponse(
                theme_id=mapping.theme_id,
                theme_name=mapping.theme_name,
                theme_category=mapping.theme_category.value,
                mapping_strength=mapping.mapping_strength,
                mapping_type=mapping.mapping_type.value,
                source=mapping.source,
                context=mapping.context
            ))
        
        logger.info(f"Retrieved {len(response_mappings)} themes for anime ID: {anime_id}")
        return response_mappings
        
    except Exception as e:
        logger.error(f"Error getting themes for anime {anime_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting themes: {str(e)}")

@router.get("/themes", response_model=List[ThemeDefinitionResponse])
async def get_available_themes(
    mapper: SimpleThemeMapper = Depends(get_theme_mapper)
) -> List[ThemeDefinitionResponse]:
    """
    Get all available themes.
    
    Returns the complete list of themes that can be mapped to anime.
    """
    try:
        themes = mapper.get_available_themes()
        
        response_themes = []
        for theme in themes:
            response_themes.append(ThemeDefinitionResponse(
                id=theme["id"],
                name=theme["name"],
                category=theme["category"].value,
                description=theme["description"]
            ))
        
        logger.info(f"Retrieved {len(response_themes)} available themes")
        return response_themes
        
    except Exception as e:
        logger.error(f"Error getting available themes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting themes: {str(e)}")

@router.get("/stats", response_model=MappingStatsResponse)
async def get_mapping_stats(
    mapper: SimpleThemeMapper = Depends(get_theme_mapper)
) -> MappingStatsResponse:
    """
    Get mapping statistics.
    
    Returns information about the theme mapping capabilities.
    """
    try:
        stats = mapper.get_mapping_stats()
        
        return MappingStatsResponse(
            total_themes_available=stats["total_themes_available"],
            mappable_genres=stats["mappable_genres"],
            mappable_tags=stats["mappable_tags"],
            theme_categories=stats["theme_categories"],
            mapping_types=stats["mapping_types"]
        )
        
    except Exception as e:
        logger.error(f"Error getting mapping stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")

@router.get("/health")
async def theme_service_health():
    """Health check for the theme service."""
    return {
        "status": "healthy",
        "service": "Simple Theme Mapper",
        "version": "1.0.0",
        "description": "Lightweight theme mapping service for MCP/API architecture"
    }
