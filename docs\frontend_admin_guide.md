# Tahimoto Admin Frontend Documentation

## Overview

The Tahimoto Admin Portal provides a comprehensive suite of tools for content management, thematic analysis, and data visualization. Built with Vue 3, TypeScript, and Tailwind CSS, this document outlines the various components available in the admin section, their purposes, and how they interact with each other.

## Core Components

### Admin Dashboard (`AdminDashboard.vue`)

The entry point for all administrative functions. It provides access to:

- Story Management
- Theme Browser & Editor
- Story-Theme Analyzer
- User Management
- System Status

### Story Management

#### AdminSearch (`AdminSearch.vue`)

A powerful search component that allows administrators to find stories across multiple sources.

**Features:**
- Advanced filtering by media type, status, and more
- Sort options: popularity, score, trending
- Real-time search with debounce for API efficiency
- Grid-based results display with covers and basic metadata

#### StoryDetails (`StoryDetails.vue`)

Displays comprehensive information about a selected story, including:

**Features:**
- Basic metadata (title, status, score, etc.)
- Synopsis and description
- Episode/chapter information
- Tags and genres with category-based coloring
- Relations to other stories
- Recommendations engine integration
- "Analyze Themes" button to send the story to ThemeStoryConnector

### Theme Management

#### ThemeBrowser (`themes/ThemeBrowser.vue`)

Allows browsing, filtering, and management of the theme taxonomy.

**Features:**
- Category-based filtering and organization
- Theme relationship visualization
- Creation, editing, and status management of themes
- Quick-search functionality

#### ThemeEditor (`themes/ThemeEditor.vue`)

Interface for creating and editing individual themes.

**Features:**
- Theme metadata editing (name, description, category)
- Parent/child relationship management
- Related theme connections
- Status management

#### ThemeRelationshipGraph (`themes/ThemeRelationshipGraph.vue`)

A visual network graph representation of theme relationships.

**Features:**
- Interactive D3-based visualization
- Relationship type color coding
- Zoom and pan capabilities
- Filtering by theme category and relationship type

### Theme Analysis

#### ThemeStoryConnector (`themes/ThemeStoryConnector.vue`)

The core component for connecting stories to themes based on their tags, genres, and other metadata.

**Features:**
- Story selection via search or direct linking from StoryDetails
- Tag-to-theme connection visualization
- Confidence scoring for thematic connections
- Detailed breakdown of why certain themes connect to specific tags
- Genre-based theme connections

#### TagThemeConnections (inside `ThemeStoryConnector.vue`)

A subcomponent that visualizes how story tags connect to themes in the taxonomy.

**Features:**
- Story tag display with categorization
- Theme connection visualization
- Confidence scoring and detailed connection explanations
- Advanced semantic matching between tags and themes

## Tag-Theme Mapping Algorithm

The Tag-Theme mapping engine is a core functionality that connects story tags and genres to thematic elements within our taxonomy. This enables deeper analysis and better recommendations.

### Algorithm Overview

The current implementation uses a multi-level matching approach:

1. **Direct Matching**: Exact matches between tag names and theme names (highest confidence)
2. **Substring Matching**: When one string contains the other (high confidence)
3. **Word-Level Matching**: Analysis of individual words in multi-word tags and themes
4. **Description Matching**: Using theme descriptions to find semantic connections to tags
5. **Category Boosting**: Increasing confidence for matches within relevant categories
6. **Genre Integration**: Mapping genres to corresponding themes with appropriate confidence

### Confidence Scoring

Confidence scores are calculated based on:

- Match quality (exact, substring, word-level)
- Theme category relevance
- Tag category information
- Word length and specificity
- Description relevance

These scores are visualized as percentage bars in the UI to show the strength of each connection.

### Recent Improvements

Recent enhancements to the mapping algorithm include:

1. **Improved Image Handling**: 
   - Added support for high-resolution cover images with fallback mechanisms
   - Implemented SVG placeholder images for missing covers
   - Added error handling for failed image loads

2. **Enhanced Metadata Display**:
   - Added support for additional fields: popularity, episode count, season
   - Formatted numeric values for better readability
   - Organized layout for optimal information hierarchy

3. **Advanced Semantic Matching**:
   - Added word normalization to remove hyphens, spaces, and case sensitivity
   - Implemented category-based confidence boosting
   - Added scoring for genre-theme connections
   - Optimized the matching algorithm for better accuracy

4. **Data Flow Improvements**:
   - Added localStorage integration for story data transfer
   - Implemented comprehensive logging for debugging
   - Enhanced error handling throughout the component

These improvements enable more accurate connections between story content and thematic elements, providing valuable insights for content analysis and recommendation algorithms.

## Data Flow

### Story Selection Flow

1. User searches for a story in `AdminSearch`
2. User selects a story from search results
3. `StoryDetails` loads and displays comprehensive information
4. User clicks "Analyze Themes" button
5. Selected story is stored in localStorage
6. User is redirected to `ThemeStoryConnector`
7. `ThemeStoryConnector` retrieves story from localStorage
8. Tags and genres from story are analyzed against themes
9. Connections are displayed with confidence scores

### Theme Management Flow

1. User browses themes in `ThemeBrowser`
2. User selects a theme to edit
3. `ThemeEditor` loads with selected theme
4. User makes changes and saves
5. Updated theme is reflected in `ThemeBrowser`
6. Theme relationships can be visualized in `ThemeRelationshipGraph`

## Technical Implementation Details

### State Management

The admin section uses a combination of:

- Pinia stores for global state management (selected story, loading states)
- Local component state for UI-specific states
- Vue composables for lifecycle management and reactive effects

### API Integration

Components connect to backend services via:

- REST API calls (`/api/v1/stories/*`, etc.)
- GraphQL for theme and relationship management
- Data is cached appropriately to minimize API requests

### Component Design Principles

1. **Separation of Concerns**: Components focus on specific tasks
2. **Reusability**: Common UI elements are extracted into reusable components
3. **Progressive Enhancement**: Complex features are layered on top of core functionality
4. **Error Handling**: Comprehensive error states and user feedback
5. **Responsive Design**: All components work across device sizes

## Best Practices

When extending the admin components:

1. Maintain consistent naming conventions
2. Add comprehensive comments for complex logic
3. Use TypeScript interfaces for type safety
4. Keep components focused and avoid feature creep
5. Add appropriate loading states and error handling
6. Consider performance implications for data-heavy components
7. Test changes across different browsers and device sizes

## Future Improvements

Planned enhancements to the admin section include:

1. Enhanced theme relationship visualization
2. Bulk operations for theme management
3. Advanced thematic mapping based on ML models
4. User permission management for admin access
5. Internationalization support for the admin interface
6. Comprehensive analytics dashboard for content metrics 