<template>
  <div>
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-900">Theme Management</h2>
      <p class="mt-1 text-sm text-gray-600">
        Search, analyze, and manage themes across different media types.
      </p>
    </div>

    <!-- Search Section -->
    <div class="card mb-8">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Search Themes</h3>
      </div>
      <div class="card-body">
        <form @submit.prevent="handleSearch" class="space-y-4">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label for="search-query" class="block text-sm font-medium text-gray-700">
                Search Query
              </label>
              <input
                id="search-query"
                v-model="searchQuery"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Enter theme keywords..."
              />
            </div>
            <div>
              <label for="media-types" class="block text-sm font-medium text-gray-700">
                Media Types
              </label>
              <select
                id="media-types"
                v-model="selectedMediaTypes"
                multiple
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option v-for="type in mediaTypes" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>
          </div>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label for="categories" class="block text-sm font-medium text-gray-700">
                Categories
              </label>
              <select
                id="categories"
                v-model="selectedCategories"
                multiple
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option v-for="category in themeCategories" :key="category" :value="category">
                  {{ formatCategoryName(category) }}
                </option>
              </select>
            </div>
            <div class="flex items-end">
              <button
                type="submit"
                :disabled="themeStore.loading"
                class="btn-primary"
              >
                <span v-if="themeStore.loading">Searching...</span>
                <span v-else>Search Themes</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Media Analysis Section -->
    <div id="media-analysis" class="card mb-8">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Analyze Media Content</h3>
      </div>
      <div class="card-body">
        <form @submit.prevent="handleAnalyzeMedia" class="space-y-4">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label for="media-title" class="block text-sm font-medium text-gray-700">
                Title
              </label>
              <input
                id="media-title"
                v-model="mediaAnalysis.title"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Enter media title..."
              />
            </div>
            <div>
              <label for="media-type" class="block text-sm font-medium text-gray-700">
                Media Type
              </label>
              <select
                id="media-type"
                v-model="mediaAnalysis.media_type"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select media type</option>
                <option v-for="type in mediaTypes" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>
          </div>
          <div>
            <label for="media-description" class="block text-sm font-medium text-gray-700">
              Description (Optional)
            </label>
            <textarea
              id="media-description"
              v-model="mediaAnalysis.description"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Enter media description..."
            ></textarea>
          </div>
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="themeStore.loading"
              class="btn-primary"
            >
              <span v-if="themeStore.loading">Analyzing...</span>
              <span v-else>Analyze Media</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="themeStore.hasError" class="mb-6">
      <div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-2 text-sm text-red-700">
              {{ themeStore.error }}
            </div>
            <div class="mt-4">
              <button
                @click="themeStore.clearError"
                class="text-sm bg-red-100 text-red-800 hover:bg-red-200 px-3 py-1 rounded-md"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Search Results -->
    <div v-if="themeStore.searchResults" class="card mb-8">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">
          Search Results ({{ themeStore.searchResults.total_count }})
        </h3>
      </div>
      <div class="card-body">
        <div v-if="themeStore.searchResults.themes.length === 0" class="text-center py-8">
          <p class="text-gray-500">No themes found matching your criteria.</p>
        </div>
        <div v-else class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="theme in themeStore.searchResults.themes"
            :key="theme.id"
            class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <h4 class="font-medium text-gray-900">{{ theme.name }}</h4>
            <p class="text-sm text-gray-600 mt-1">{{ theme.description }}</p>
            <div class="mt-2 flex flex-wrap gap-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                {{ formatCategoryName(theme.category) }}
              </span>
              <span
                v-for="mediaType in theme.media_types"
                :key="mediaType"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {{ mediaType }}
              </span>
            </div>
            <div v-if="theme.confidence" class="mt-2">
              <div class="flex items-center">
                <span class="text-xs text-gray-500 mr-2">Confidence:</span>
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-primary-600 h-2 rounded-full"
                    :style="{ width: `${theme.confidence * 100}%` }"
                  ></div>
                </div>
                <span class="text-xs text-gray-500 ml-2">{{ Math.round(theme.confidence * 100) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Analysis Results -->
    <div v-if="analysisResults" class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Analysis Results</h3>
      </div>
      <div class="card-body">
        <div class="mb-4">
          <h4 class="font-medium text-gray-900">Detected Themes:</h4>
          <div class="mt-2 grid grid-cols-1 gap-3 sm:grid-cols-2">
            <div
              v-for="theme in analysisResults.detected_themes"
              :key="theme.id"
              class="border border-gray-200 rounded-lg p-3"
            >
              <h5 class="font-medium text-gray-900">{{ theme.name }}</h5>
              <p class="text-sm text-gray-600 mt-1">{{ theme.description }}</p>
              <div class="mt-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {{ formatCategoryName(theme.category) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useAnimeStore } from '@/stores/anime'
import { ThemeCategory, MediaType } from '@/types/theme'
import type { MediaAnalysisRequest, MediaAnalysisResponse } from '@/types/theme'

const themeStore = useThemeStore()
const animeStore = useAnimeStore()
const route = useRoute()

// Search form data
const searchQuery = ref('')
const selectedMediaTypes = ref<MediaType[]>([])
const selectedCategories = ref<ThemeCategory[]>([])

// Media analysis form data
const mediaAnalysis = ref<MediaAnalysisRequest>({
  title: '',
  description: '',
  media_type: MediaType.ANIME,
})

// Analysis results
const analysisResults = ref<MediaAnalysisResponse | null>(null)

// Constants
const mediaTypes = Object.values(MediaType)
const themeCategories = Object.values(ThemeCategory)

// Methods
function formatCategoryName(category: string): string {
  return category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
}

async function handleSearch() {
  try {
    await themeStore.searchThemes({
      query: searchQuery.value || undefined,
      media_types: selectedMediaTypes.value.length > 0 ? selectedMediaTypes.value : undefined,
      categories: selectedCategories.value.length > 0 ? selectedCategories.value : undefined,
    })
  } catch (error) {
    console.error('Search failed:', error)
  }
}

async function handleAnalyzeMedia() {
  try {
    analysisResults.value = await themeStore.analyzeMedia(mediaAnalysis.value)
  } catch (error) {
    console.error('Analysis failed:', error)
  }
}

// Watch for anime parameter in route
watch(() => route.query.anime, async (animeId) => {
  if (animeId && typeof animeId === 'string') {
    try {
      const anime = await animeStore.getAnime(animeId)
      if (anime) {
        // Pre-populate the media analysis form
        mediaAnalysis.value = {
          title: anime.title_english || anime.title_romaji || anime.title_native || '',
          description: anime.synopsis || anime.description || '',
          media_type: MediaType.ANIME,
        }
      }
    } catch (error) {
      console.error('Failed to load anime:', error)
    }
  }
}, { immediate: true })

// Initialize
onMounted(async () => {
  try {
    await themeStore.fetchCapabilities()
    await themeStore.fetchCategories()

    // Check for query parameters from anime detail page
    if (route.query.title && route.query.description) {
      mediaAnalysis.value = {
        title: route.query.title as string,
        description: route.query.description as string,
        media_type: MediaType.ANIME,
      }

      // Scroll to the analysis section
      setTimeout(() => {
        const analysisSection = document.getElementById('media-analysis')
        if (analysisSection) {
          analysisSection.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    }
  } catch (error) {
    console.error('Failed to initialize:', error)
  }
})
</script>
