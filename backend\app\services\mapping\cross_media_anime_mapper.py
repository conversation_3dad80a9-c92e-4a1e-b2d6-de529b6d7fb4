"""
Cross-media anime theme mapper that connects AniList metadata to our cross-media theme database.
This mapper creates simple, direct mappings for MVP functionality.
"""
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from app.db.neo4j_session import driver
from app.schemas.theme_category import ThemeCategory

logger = logging.getLogger(__name__)

class CrossMediaAnimeMapper:
    """Maps AniList anime metadata to cross-media themes in the database."""
    
    def __init__(self):
        # Direct genre to theme name mappings for MVP
        self.genre_to_theme_map = {
            # Mood themes
            "Comedy": "Comedic",
            "Romance": "Romantic", 
            "Horror": "Dark",
            "Slice of Life": "Cozy",
            "Thriller": "Tense",
            
            # Narrative structure themes
            "Adventure": "Journey",
            "Mystery": "Mystery",
            "Drama": "Tragedy",
            
            # Setting themes
            "Fantasy": "Fantasy World",
            "Sci-Fi": "Sci-Fi Future",
            "Historical": "Historical",
            "Sports": "School Life",  # Many sports anime are school-based
            
            # Character dynamics (inferred from genres)
            "Mecha": "Team Dynamics",  # Mecha often involves team coordination
            "Music": "Friendship",  # Music anime often focus on friendships
        }
        
        # AniList tag to theme name mappings for MVP
        self.tag_to_theme_map = {
            # Mood themes
            "Iyashikei": "Cozy",
            "Cute Girls Doing Cute Things": "Cozy", 
            "Psychological": "Tense",
            "Gore": "Dark",
            "Tragedy": "Dark",
            "Parody": "Comedic",
            "Satire": "Comedic",
            
            # Narrative structure themes
            "Coming of Age": "Coming of Age",
            "Episodic": "Slice of Life",
            "Tournament": "Tournament",
            "Ensemble Cast": "Ensemble",
            
            # Character dynamics
            "Found Family": "Found Family",
            "Rivalry": "Rivalry",
            "Mentor": "Mentor-Student",
            "Teacher": "Mentor-Student",
            "Friendship": "Friendship",
            "Anti-Hero": "Lone Wolf",
            "Loner": "Lone Wolf",
            "Team": "Team Dynamics",
            "Band": "Team Dynamics",
            
            # Setting themes
            "School": "School Life",
            "School Club": "School Life",
            "High School": "School Life",
            "College": "School Life",
            "Urban": "Urban Modern",
            "Rural": "Rural",
            "Countryside": "Rural",
            "Post-Apocalyptic": "Sci-Fi Future",
            "Cyberpunk": "Sci-Fi Future",
            "Isekai": "Fantasy World",
            "Magic": "Fantasy World",
            "Medieval": "Historical",
            "Feudal Japan": "Historical",
        }
        
        # Cache for theme lookups
        self._theme_cache = {}
    
    async def get_theme_by_name(self, theme_name: str) -> Optional[Dict[str, Any]]:
        """Get theme from database by name, with caching."""
        if theme_name in self._theme_cache:
            return self._theme_cache[theme_name]
        
        async with driver.session() as session:
            result = await session.run(
                "MATCH (t:Theme {name: $name}) RETURN t",
                {"name": theme_name}
            )
            record = await result.single()
            
            if record:
                theme = dict(record["t"])
                self._theme_cache[theme_name] = theme
                return theme
            
            return None
    
    async def map_anime_to_themes(self, anime_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Map anime metadata to cross-media themes.
        
        Args:
            anime_metadata: Dictionary containing AniList anime data with genres, tags, etc.
            
        Returns:
            List of theme mappings with confidence scores and mapping types
        """
        theme_mappings = []
        mapped_theme_names = set()  # Avoid duplicates
        
        # Extract metadata
        genres = anime_metadata.get("genres", [])
        tags = anime_metadata.get("tags", [])
        title = anime_metadata.get("title_romaji", "Unknown")
        
        logger.info(f"Mapping themes for anime: {title}")
        logger.debug(f"Genres: {genres}")
        logger.debug(f"Tags: {[tag.get('name') if isinstance(tag, dict) else tag for tag in tags]}")
        
        # Map genres to themes
        for genre in genres:
            if genre in self.genre_to_theme_map:
                theme_name = self.genre_to_theme_map[genre]
                if theme_name not in mapped_theme_names:
                    theme = await self.get_theme_by_name(theme_name)
                    if theme:
                        theme_mappings.append({
                            "theme": theme,
                            "mapping_strength": 0.8,  # High confidence for genre mappings
                            "mapping_type": "PRIMARY",
                            "source": f"genre:{genre}",
                            "context": f"Mapped from AniList genre '{genre}'"
                        })
                        mapped_theme_names.add(theme_name)
                        logger.debug(f"Mapped genre '{genre}' to theme '{theme_name}'")
        
        # Map tags to themes
        for tag in tags:
            tag_name = tag.get("name") if isinstance(tag, dict) else str(tag)
            tag_rank = tag.get("rank", 0) if isinstance(tag, dict) else 0
            
            if tag_name in self.tag_to_theme_map:
                theme_name = self.tag_to_theme_map[tag_name]
                if theme_name not in mapped_theme_names:
                    theme = await self.get_theme_by_name(theme_name)
                    if theme:
                        # Adjust confidence based on tag rank (higher rank = more relevant)
                        confidence = 0.6 + (tag_rank / 100.0 * 0.2) if tag_rank else 0.6
                        confidence = min(confidence, 0.8)  # Cap at 0.8
                        
                        mapping_type = "PRIMARY" if confidence >= 0.7 else "SECONDARY"
                        
                        theme_mappings.append({
                            "theme": theme,
                            "mapping_strength": confidence,
                            "mapping_type": mapping_type,
                            "source": f"tag:{tag_name}",
                            "context": f"Mapped from AniList tag '{tag_name}' (rank: {tag_rank})"
                        })
                        mapped_theme_names.add(theme_name)
                        logger.debug(f"Mapped tag '{tag_name}' to theme '{theme_name}' (confidence: {confidence:.2f})")
        
        # Add some heuristic mappings based on common patterns
        await self._add_heuristic_mappings(anime_metadata, theme_mappings, mapped_theme_names)
        
        logger.info(f"Mapped {len(theme_mappings)} themes for anime '{title}'")
        return theme_mappings
    
    async def _add_heuristic_mappings(self, anime_metadata: Dict[str, Any], 
                                    theme_mappings: List[Dict[str, Any]], 
                                    mapped_theme_names: set):
        """Add heuristic theme mappings based on common anime patterns."""
        genres = anime_metadata.get("genres", [])
        tags = anime_metadata.get("tags", [])
        tag_names = [tag.get("name") if isinstance(tag, dict) else str(tag) for tag in tags]
        
        # School + Coming of Age heuristic
        if any("School" in tag for tag in tag_names) and "Coming of Age" not in mapped_theme_names:
            theme = await self.get_theme_by_name("Coming of Age")
            if theme:
                theme_mappings.append({
                    "theme": theme,
                    "mapping_strength": 0.5,
                    "mapping_type": "SECONDARY",
                    "source": "heuristic:school_coming_of_age",
                    "context": "School setting often involves coming-of-age themes"
                })
                mapped_theme_names.add("Coming of Age")
        
        # Romance + Friendship heuristic
        if "Romance" in genres and "Friendship" not in mapped_theme_names:
            theme = await self.get_theme_by_name("Friendship")
            if theme:
                theme_mappings.append({
                    "theme": theme,
                    "mapping_strength": 0.4,
                    "mapping_type": "TERTIARY",
                    "source": "heuristic:romance_friendship",
                    "context": "Romance anime often feature friendship themes"
                })
                mapped_theme_names.add("Friendship")
        
        # Add nostalgic theme for historical anime
        if "Historical" in genres and "Nostalgic" not in mapped_theme_names:
            theme = await self.get_theme_by_name("Nostalgic")
            if theme:
                theme_mappings.append({
                    "theme": theme,
                    "mapping_strength": 0.6,
                    "mapping_type": "SECONDARY", 
                    "source": "heuristic:historical_nostalgic",
                    "context": "Historical settings often evoke nostalgia"
                })
                mapped_theme_names.add("Nostalgic")
    
    async def get_mapping_stats(self) -> Dict[str, Any]:
        """Get statistics about the mapping system."""
        async with driver.session() as session:
            # Count total themes
            result = await session.run("MATCH (t:Theme) RETURN count(t) as total")
            record = await result.single()
            total_themes = record["total"]
            
            # Count mappable genres and tags
            mappable_genres = len(self.genre_to_theme_map)
            mappable_tags = len(self.tag_to_theme_map)
            
            return {
                "total_themes_in_db": total_themes,
                "mappable_genres": mappable_genres,
                "mappable_tags": mappable_tags,
                "genre_mappings": dict(self.genre_to_theme_map),
                "tag_mappings": dict(self.tag_to_theme_map)
            }
    
    def clear_cache(self):
        """Clear the theme cache."""
        self._theme_cache.clear()
        logger.info("Theme cache cleared")
