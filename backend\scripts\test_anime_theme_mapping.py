#!/usr/bin/env python
"""
Test script for anime theme mapping functionality.
Tests the cross-media anime mapper with sample anime data.
"""
import asyncio
import json
import sys
from pathlib import Path

# Add the parent directory to the sys.path for imports to work
sys.path.append(str(Path(__file__).parent.parent))

from app.services.theme_mapping_service import ThemeMappingService
from app.services.mapping.cross_media_anime_mapper import CrossMediaAnimeMapper

# Sample anime data for testing
SAMPLE_ANIME_DATA = [
    {
        "id": "test_anime_1",
        "title_romaji": "Your Name",
        "title_english": "Your Name",
        "genres": ["Romance", "Drama", "Slice of Life"],
        "tags": [
            {"name": "Coming of Age", "rank": 85},
            {"name": "School", "rank": 70},
            {"name": "Friendship", "rank": 60}
        ],
        "synopsis": "A romantic drama about two teenagers who mysteriously swap bodies."
    },
    {
        "id": "test_anime_2", 
        "title_romaji": "Attack on Titan",
        "title_english": "Attack on Titan",
        "genres": ["Action", "Drama", "Fantasy"],
        "tags": [
            {"name": "Military", "rank": 90},
            {"name": "Tragedy", "rank": 85},
            {"name": "Gore", "rank": 80},
            {"name": "Ensemble Cast", "rank": 75}
        ],
        "synopsis": "Humanity fights for survival against giant humanoid Titans."
    },
    {
        "id": "test_anime_3",
        "title_romaji": "K-On!",
        "title_english": "K-On!",
        "genres": ["Comedy", "Music", "Slice of Life"],
        "tags": [
            {"name": "School", "rank": 95},
            {"name": "School Club", "rank": 90},
            {"name": "Cute Girls Doing Cute Things", "rank": 85},
            {"name": "Band", "rank": 80},
            {"name": "Friendship", "rank": 75}
        ],
        "synopsis": "High school girls form a light music club and learn to play instruments."
    },
    {
        "id": "test_anime_4",
        "title_romaji": "Spirited Away",
        "title_english": "Spirited Away",
        "genres": ["Adventure", "Family", "Fantasy"],
        "tags": [
            {"name": "Coming of Age", "rank": 90},
            {"name": "Magic", "rank": 85},
            {"name": "Isekai", "rank": 80}
        ],
        "synopsis": "A young girl enters a world ruled by gods and witches."
    }
]

async def test_anime_mapper():
    """Test the cross-media anime mapper."""
    print("=== Testing Cross-Media Anime Mapper ===\n")
    
    mapper = CrossMediaAnimeMapper()
    
    # Test mapping statistics
    print("1. Mapper Statistics:")
    stats = await mapper.get_mapping_stats()
    print(f"   Total themes in database: {stats['total_themes_in_db']}")
    print(f"   Mappable genres: {stats['mappable_genres']}")
    print(f"   Mappable tags: {stats['mappable_tags']}")
    print()
    
    # Test individual anime mappings
    print("2. Individual Anime Mappings:")
    for anime in SAMPLE_ANIME_DATA:
        print(f"\n--- {anime['title_english']} ---")
        print(f"Genres: {anime['genres']}")
        print(f"Tags: {[tag['name'] for tag in anime['tags']]}")
        
        mappings = await mapper.map_anime_to_themes(anime)
        print(f"Mapped themes ({len(mappings)}):")
        
        for mapping in mappings:
            theme = mapping["theme"]
            print(f"  • {theme['name']} ({theme['category']})")
            print(f"    Strength: {mapping['mapping_strength']:.2f}")
            print(f"    Type: {mapping['mapping_type']}")
            print(f"    Source: {mapping['source']}")
            print(f"    Context: {mapping['context']}")
        
        if not mappings:
            print("  No themes mapped!")
    
    print("\n" + "="*50)

async def test_theme_mapping_service():
    """Test the theme mapping service."""
    print("=== Testing Theme Mapping Service ===\n")
    
    service = ThemeMappingService()
    
    # Test service statistics
    print("1. Service Statistics:")
    try:
        stats = await service.get_mapping_statistics()
        print(f"   Total mappings: {stats['total_mappings']}")
        print(f"   Anime with mappings: {stats['anime_with_mappings']}")
        print(f"   Average mappings per anime: {stats['average_mappings_per_anime']}")
        print(f"   Mappings by type: {stats['mappings_by_type']}")
    except Exception as e:
        print(f"   Error getting statistics: {str(e)}")
    
    print("\n2. Testing Mapping Creation:")
    
    # Test creating mappings for one anime
    test_anime = SAMPLE_ANIME_DATA[0]  # Your Name
    print(f"\nCreating mappings for: {test_anime['title_english']}")
    
    try:
        # Note: This would require the anime to exist in the database
        # For now, we'll just test the mapping logic
        mappings = await service.anime_mapper.map_anime_to_themes(test_anime)
        print(f"Would create {len(mappings)} mappings:")
        
        for mapping in mappings:
            theme = mapping["theme"]
            print(f"  • {theme['name']} (strength: {mapping['mapping_strength']:.2f})")
    
    except Exception as e:
        print(f"   Error creating mappings: {str(e)}")
    
    print("\n" + "="*50)

async def test_theme_lookup():
    """Test theme lookup functionality."""
    print("=== Testing Theme Lookup ===\n")
    
    mapper = CrossMediaAnimeMapper()
    
    # Test looking up specific themes
    test_theme_names = ["Cozy", "Journey", "Found Family", "School Life", "NonExistent"]
    
    print("Theme lookup results:")
    for theme_name in test_theme_names:
        theme = await mapper.get_theme_by_name(theme_name)
        if theme:
            print(f"  ✓ {theme_name}: Found (ID: {theme['id']}, Category: {theme['category']})")
        else:
            print(f"  ✗ {theme_name}: Not found")
    
    print("\n" + "="*50)

async def main():
    """Run all tests."""
    print("Cross-Media Anime Theme Mapping Test Suite")
    print("=" * 50)
    
    try:
        await test_theme_lookup()
        await test_anime_mapper()
        await test_theme_mapping_service()
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
