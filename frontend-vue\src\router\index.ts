import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Admin from '@/views/Admin.vue'
import AdminThemes from '@/views/admin/Themes.vue'
import AdminThemeStoryConnector from '@/views/admin/ThemeStoryConnector.vue'
import AnimeDetail from '@/views/AnimeDetail.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home,
    },
    {
      path: '/anime/:id',
      name: 'AnimeDetail',
      component: AnimeDetail,
      props: true,
    },
    {
      path: '/admin',
      name: 'Admin',
      component: Admin,
      children: [
        {
          path: '',
          redirect: '/admin/themes',
        },
        {
          path: 'themes',
          name: 'AdminThemes',
          component: AdminThemes,
        },
        {
          path: 'theme-story-connector',
          name: 'AdminThemeStoryConnector',
          component: AdminThemeStoryConnector,
        },
      ],
    },
  ],
})

export default router
