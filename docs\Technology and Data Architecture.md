# Tahimoto - Technology and Data Architecture

This document outlines the core technologies, data storage strategies, and caching mechanisms employed by the Tahimoto cross-media story recommendation platform. It explains the rationale behind these choices and details the workflow of the recommendation engine.

## Core Technologies and Rationale

Tahimoto leverages a multi-technology approach, selecting tools best suited for specific tasks and data types:

* **Backend API (FastAPI - Python):**
    * **Rationale:** Python's ecosystem is rich in libraries for data science and machine learning, and FastAPI provides a modern, high-performance framework for building APIs. Its asynchronous capabilities are crucial for handling I/O-bound operations efficiently.
* **GraphQL API (Strawberry - Python):**
    * **Rationale:** GraphQL is ideal for exposing the thematic relationships between media. It allows clients (frontend, agentic engine) to request precisely the data they need, avoiding over-fetching or under-fetching. Its schema provides strong typing and introspection capabilities.
* **SQL Database (PostgreSQL):**
    * **Rationale:** PostgreSQL serves as the primary persistent storage for structured data: user accounts, core media metadata (titles, creators, basic descriptions), and explicit user preferences. It offers robust transactional capabilities, data integrity, and is well-suited for relational data.
* **Vector Database (ChromaDB):**
    * **Rationale:** A vector database is essential for understanding the semantic similarity between media based on their content and thematic nuances. ChromaDB provides a lightweight, easy-to-use, and performant solution for storing and querying vector embeddings. This allows for finding media with implicit thematic connections.
* **Caching System (Redis):**
    * **Rationale:** Redis acts as a high-speed in-memory data store, crucial for caching frequently accessed data (thematic mappings, user interaction data, embedding results). This significantly reduces latency and improves the overall responsiveness of the platform.
* **Frontend (Vue + Vite + Tailwind):**
    * **Rationale:** A modern component-based architecture for building a user-friendly and responsive interface. Vue 3 with Composition API provides excellent developer experience, TypeScript provides type safety, and Vite offers fast development with hot module replacement.
* **Agentic Engine (Custom Python):**
    * **Rationale:** Provides the intelligent automation layer for tasks like thematic mapping analysis, recommendation refinement, and potentially content generation. It orchestrates interactions between the different data stores and the LLM.
* **LLM Integration (Planned):**
    * **Rationale:** To enhance thematic understanding, generate more nuanced recommendations, and potentially provide explanations for recommendations. LLMs can also assist in the thematic mapping process.

## Directory Structure

```
backend/
├── app/
│   ├── api/                 # API layer (FastAPI endpoints)
│   ├── core/               # Core functionality
│   │   ├── cache.py        # Redis cache management
│   │   ├── config.py       # Application configuration
│   │   ├── vector_store.py # ChromaDB management
│   │   └── redis.py        # Redis connection
│   ├── crud/               # Database operations
│   ├── db/                 # Database management
│   ├── models/             # SQLAlchemy models
│   ├── schemas/            # Pydantic schemas
│   ├── services/           # Business logic
│   │   ├── anilist.py      # AniList integration
│   │   ├── theme.py        # Theme processing
│   │   ├── vector.py       # Vector embedding services
│   │   └── agentic.py      # Agentic engine logic
│   └── graphql/            # GraphQL implementation
```

## Data Storage Strategies

Tahimoto employs a multi-database strategy, optimizing each storage solution for its specific purpose:

* **PostgreSQL (Local):**
    * **Stores:** User accounts, core media metadata, explicit user preferences
    * **Why:** Reliable, transactional, and suitable for structured relational data
* **Redis (Local):**
    * **Stores:** Cached thematic mappings, recent user interactions, session data
    * **Why:** Extremely fast read/write operations, ideal for caching and real-time data
* **ChromaDB (Local with Cloud Backup):**
    * **Stores:** Vector embeddings of media content, thematic embeddings
    * **Why:** Efficient similarity searches, semantic understanding of content
* **GraphQL Layer:**
    * **Provides:** Unified interface for thematic mappings and relationships
    * **Why:** Optimized for querying complex relationships between media and themes

## Caching Strategy

The caching system is designed to optimize performance and reduce database load:

* **Redis Caching Layers:**
    * **L1 - Hot Data (1 hour TTL):**
        * Active user sessions
        * Recent search results
        * Current user preferences
    * **L2 - Warm Data (24 hour TTL):**
        * Popular media metadata
        * Common theme mappings
        * Frequently accessed embeddings
    * **L3 - Analytics (7 day TTL):**
        * Usage statistics
        * Cache hit rates
        * Performance metrics

## Vector Database Integration

ChromaDB integration is crucial for semantic understanding:

* **Collections:**
    * `media_embeddings`: Story content embeddings
    * `theme_embeddings`: Theme and concept embeddings
    * `user_preference_embeddings`: User preference vectors

* **Vector Operations:**
    * Similarity search for recommendations
    * Theme clustering and analysis
    * Content-based filtering
    * Semantic search capabilities

* **Integration Points:**
    * Theme analysis service
    * Recommendation engine
    * Search functionality
    * Content discovery features

## Recommendation Workflow

The recommendation process involves multiple components:

1. **Initial Context Gathering:**
   * User profile from PostgreSQL
   * Recent interactions from Redis
   * Explicit preferences

2. **Vector Similarity Processing:**
   * Generate query embeddings
   * Perform similarity search in ChromaDB
   * Score and rank results

3. **Theme Analysis:**
   * Map to universal themes via GraphQL
   * Apply thematic filters
   * Calculate theme relevance scores

4. **Result Processing:**
   * Merge and rank results
   * Apply business rules
   * Cache final recommendations

5. **Response Generation:**
   * Format results for client
   * Include explanation metadata
   * Cache response in Redis

## Performance Considerations

* **Caching Strategy:**
    * Multi-level caching with Redis
    * Predictive caching for popular content
    * Intelligent cache invalidation

* **Vector Search Optimization:**
    * Indexed similarity search
    * Batch processing for updates
    * Asynchronous embedding generation

* **Database Load Management:**
    * Connection pooling
    * Query optimization
    * Efficient join strategies

## Monitoring and Observability

* **Performance Metrics:**
    * Response times
    * Cache hit rates
    * Database query performance
    * Vector search latency

* **Error Tracking:**
    * Structured logging
    * Error aggregation
    * Alert thresholds

* **Resource Utilization:**
    * Memory usage
    * CPU utilization
    * Network bandwidth
    * Storage capacity

## Future Enhancements

1. **LLM Integration:**
    * Enhanced theme mapping
    * Natural language search
    * Recommendation explanations

2. **Scalability:**
    * Distributed vector search
    * Horizontal scaling
    * Load balancing

3. **Advanced Features:**
    * Personalization improvements
    * Cross-media discovery
    * Content generation
    * Interactive recommendations 