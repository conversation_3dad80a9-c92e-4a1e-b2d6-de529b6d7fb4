@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn text-primary-700 bg-primary-100 hover:bg-primary-200 focus:ring-primary-500;
  }

  .btn-outline {
    @apply btn text-primary-700 bg-white border-primary-300 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }
  
  .card-header {
    @apply px-4 py-5 sm:px-6 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-4 py-5 sm:p-6;
  }
}
