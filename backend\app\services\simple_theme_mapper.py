"""
Simplified Theme Mapping Service for MCP/API Architecture

This service provides theme mapping without heavy database dependencies.
Themes are computed on-demand and can be cached in Redis for performance.
Perfect for MCP server integration and lightweight API responses.
"""
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ThemeCategory(str, Enum):
    """Theme categories for cross-media analysis."""
    NARRATIVE_STRUCTURE = "NARRATIVE_STRUCTURE"
    CHARACTER_DEVELOPMENT = "CHARACTER_DEVELOPMENT"
    MOOD = "MOOD"
    SETTING = "SETTING"
    CONFLICT = "CONFLICT"
    RELATIONSHIPS = "RELATIONSHIPS"
    PHILOSOPHY = "PHILOSOPHY"
    SOCIAL_COMMENTARY = "SOCIAL_COMMENTARY"

class MappingType(str, Enum):
    """Mapping confidence types."""
    PRIMARY = "PRIMARY"      # High confidence, core theme
    SECONDARY = "SECONDARY"  # Medium confidence, supporting theme
    TERTIARY = "TERTIARY"    # Low confidence, minor theme

@dataclass
class ThemeMapping:
    """Simple theme mapping result."""
    theme_id: str
    theme_name: str
    theme_category: ThemeCategory
    mapping_strength: float
    mapping_type: MappingType
    source: str
    context: str

class SimpleThemeMapper:
    """
    Lightweight theme mapper that works without database dependencies.
    Perfect for MCP/API architecture where themes are computed on-demand.
    """
    
    def __init__(self):
        self.genre_mappings = self._init_genre_mappings()
        self.tag_mappings = self._init_tag_mappings()
        self.theme_definitions = self._init_theme_definitions()
    
    def _init_genre_mappings(self) -> Dict[str, List[str]]:
        """Initialize genre to theme mappings."""
        return {
            # Action/Adventure
            "Action": ["Action", "Adventure", "Heroism"],
            "Adventure": ["Adventure", "Discovery", "Journey"],
            "Martial Arts": ["Combat", "Discipline", "Honor"],
            
            # Drama/Emotion
            "Drama": ["Tragedy", "Emotional Growth", "Human Condition"],
            "Romance": ["Love", "Relationships", "Emotional Connection"],
            "Slice of Life": ["Daily Life", "Realism", "Simple Pleasures"],
            
            # Dark/Mature
            "Horror": ["Dark", "Fear", "Supernatural"],
            "Thriller": ["Suspense", "Tension", "Mystery"],
            "Psychological": ["Mental State", "Identity Crisis", "Inner Conflict"],
            
            # Fantasy/Sci-Fi
            "Fantasy": ["Magic", "Otherworld", "Mythology"],
            "Sci-Fi": ["Technology", "Future", "Scientific Progress"],
            "Supernatural": ["Paranormal", "Spiritual", "Beyond Reality"],
            
            # Comedy/Light
            "Comedy": ["Humor", "Lighthearted", "Entertainment"],
            "Ecchi": ["Sexuality", "Coming of Age", "Relationships"],
            
            # Specific Themes
            "Military": ["War", "Duty", "Sacrifice"],
            "Sports": ["Competition", "Teamwork", "Personal Growth"],
            "Music": ["Artistic Expression", "Passion", "Performance"],
            "School": ["Education", "Youth", "Social Dynamics"],
        }
    
    def _init_tag_mappings(self) -> Dict[str, List[str]]:
        """Initialize tag to theme mappings."""
        return {
            # Character Development
            "Coming of Age": ["Personal Growth", "Maturity", "Self Discovery"],
            "Character Development": ["Personal Growth", "Change", "Evolution"],
            "Friendship": ["Bonds", "Loyalty", "Support"],
            "Rivalry": ["Competition", "Motivation", "Growth Through Conflict"],
            
            # Narrative Elements
            "Plot Twist": ["Surprise", "Revelation", "Narrative Complexity"],
            "Time Travel": ["Temporal Themes", "Causality", "Second Chances"],
            "Amnesia": ["Identity", "Memory", "Self Discovery"],
            "Revenge": ["Justice", "Retribution", "Moral Ambiguity"],
            
            # Social Themes
            "School Life": ["Education", "Social Hierarchy", "Youth Culture"],
            "Work Life": ["Career", "Adult Responsibilities", "Social Structure"],
            "Family": ["Kinship", "Generational Conflict", "Support Systems"],
            
            # Philosophical
            "Philosophy": ["Existentialism", "Meaning of Life", "Ethics"],
            "Religion": ["Spirituality", "Faith", "Moral Framework"],
            "Politics": ["Power", "Social Change", "Ideology"],
            
            # Emotional
            "Tragedy": ["Loss", "Suffering", "Human Fragility"],
            "Hope": ["Optimism", "Perseverance", "Future"],
            "Despair": ["Hopelessness", "Darkness", "Struggle"],
        }
    
    def _init_theme_definitions(self) -> Dict[str, Dict[str, Any]]:
        """Initialize theme definitions with metadata."""
        themes = {}
        
        # Generate theme definitions from mappings
        all_themes = set()
        for genre_themes in self.genre_mappings.values():
            all_themes.update(genre_themes)
        for tag_themes in self.tag_mappings.values():
            all_themes.update(tag_themes)
        
        # Create theme definitions
        theme_categories = {
            # Narrative Structure
            "Action": ThemeCategory.NARRATIVE_STRUCTURE,
            "Adventure": ThemeCategory.NARRATIVE_STRUCTURE,
            "Tragedy": ThemeCategory.NARRATIVE_STRUCTURE,
            "Comedy": ThemeCategory.NARRATIVE_STRUCTURE,
            
            # Character Development
            "Personal Growth": ThemeCategory.CHARACTER_DEVELOPMENT,
            "Coming of Age": ThemeCategory.CHARACTER_DEVELOPMENT,
            "Identity Crisis": ThemeCategory.CHARACTER_DEVELOPMENT,
            "Self Discovery": ThemeCategory.CHARACTER_DEVELOPMENT,
            
            # Mood
            "Dark": ThemeCategory.MOOD,
            "Lighthearted": ThemeCategory.MOOD,
            "Suspense": ThemeCategory.MOOD,
            "Hope": ThemeCategory.MOOD,
            "Despair": ThemeCategory.MOOD,
            
            # Relationships
            "Love": ThemeCategory.RELATIONSHIPS,
            "Friendship": ThemeCategory.RELATIONSHIPS,
            "Family": ThemeCategory.RELATIONSHIPS,
            "Rivalry": ThemeCategory.RELATIONSHIPS,
            
            # Philosophy
            "Existentialism": ThemeCategory.PHILOSOPHY,
            "Ethics": ThemeCategory.PHILOSOPHY,
            "Meaning of Life": ThemeCategory.PHILOSOPHY,
            
            # Social Commentary
            "Social Hierarchy": ThemeCategory.SOCIAL_COMMENTARY,
            "Power": ThemeCategory.SOCIAL_COMMENTARY,
            "Justice": ThemeCategory.SOCIAL_COMMENTARY,
        }
        
        for theme_name in all_themes:
            theme_id = f"theme_{theme_name.lower().replace(' ', '_')}"
            themes[theme_name] = {
                "id": theme_id,
                "name": theme_name,
                "category": theme_categories.get(theme_name, ThemeCategory.NARRATIVE_STRUCTURE),
                "description": f"Theme representing {theme_name.lower()} elements in media"
            }
        
        return themes
    
    async def map_anime_to_themes(self, anime_metadata: Dict[str, Any]) -> List[ThemeMapping]:
        """
        Map anime metadata to themes without database dependencies.
        
        Args:
            anime_metadata: Dictionary containing anime data (genres, tags, etc.)
            
        Returns:
            List of ThemeMapping objects
        """
        try:
            title = anime_metadata.get("title_romaji", "Unknown")
            genres = anime_metadata.get("genres", [])
            tags = anime_metadata.get("tags", [])
            
            logger.info(f"Mapping themes for anime: {title}")
            logger.debug(f"Genres: {genres}")
            logger.debug(f"Tags: {[tag.get('name') if isinstance(tag, dict) else tag for tag in tags]}")
            
            theme_mappings = []
            mapped_themes = set()
            
            # Map genres to themes
            for genre in genres:
                if genre in self.genre_mappings:
                    for theme_name in self.genre_mappings[genre]:
                        if theme_name not in mapped_themes and theme_name in self.theme_definitions:
                            theme_def = self.theme_definitions[theme_name]
                            mapping = ThemeMapping(
                                theme_id=theme_def["id"],
                                theme_name=theme_name,
                                theme_category=theme_def["category"],
                                mapping_strength=0.8,  # High confidence for genre mappings
                                mapping_type=MappingType.PRIMARY,
                                source=f"genre:{genre}",
                                context=f"Mapped from AniList genre '{genre}'"
                            )
                            theme_mappings.append(mapping)
                            mapped_themes.add(theme_name)
            
            # Map tags to themes
            for tag in tags:
                tag_name = tag.get("name") if isinstance(tag, dict) else str(tag)
                tag_rank = tag.get("rank", 0) if isinstance(tag, dict) else 0
                
                if tag_name in self.tag_mappings:
                    for theme_name in self.tag_mappings[tag_name]:
                        if theme_name not in mapped_themes and theme_name in self.theme_definitions:
                            theme_def = self.theme_definitions[theme_name]
                            
                            # Adjust confidence based on tag rank
                            confidence = 0.6 + (tag_rank / 100.0 * 0.2) if tag_rank else 0.6
                            confidence = min(confidence, 0.8)
                            
                            mapping_type = MappingType.PRIMARY if confidence >= 0.7 else MappingType.SECONDARY
                            
                            mapping = ThemeMapping(
                                theme_id=theme_def["id"],
                                theme_name=theme_name,
                                theme_category=theme_def["category"],
                                mapping_strength=confidence,
                                mapping_type=mapping_type,
                                source=f"tag:{tag_name}",
                                context=f"Mapped from AniList tag '{tag_name}' (rank: {tag_rank})"
                            )
                            theme_mappings.append(mapping)
                            mapped_themes.add(theme_name)
            
            logger.info(f"Successfully mapped {len(theme_mappings)} themes for anime: {title}")
            return theme_mappings
            
        except Exception as e:
            logger.error(f"Error mapping themes for anime: {str(e)}")
            return []
    
    def get_available_themes(self) -> List[Dict[str, Any]]:
        """Get all available themes."""
        return list(self.theme_definitions.values())
    
    def get_mapping_stats(self) -> Dict[str, Any]:
        """Get mapping statistics."""
        return {
            "total_themes_available": len(self.theme_definitions),
            "mappable_genres": len(self.genre_mappings),
            "mappable_tags": len(self.tag_mappings),
            "theme_categories": len(ThemeCategory),
            "mapping_types": len(MappingType)
        }
