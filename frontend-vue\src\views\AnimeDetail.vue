<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="animeStore.loading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="animeStore.hasError" class="max-w-4xl mx-auto px-4 py-8">
      <div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error Loading Anime</h3>
            <div class="mt-2 text-sm text-red-700">
              <p>{{ animeStore.error }}</p>
            </div>
            <div class="mt-4">
              <button @click="loadAnime" class="btn-primary">Try Again</button>
              <router-link to="/" class="btn-secondary ml-3">Back to Home</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else-if="anime" class="max-w-6xl mx-auto px-4 py-8">
      <!-- Header with Banner -->
      <div v-if="anime.banner_image" class="relative mb-8 rounded-lg overflow-hidden">
        <img 
          :src="anime.banner_image" 
          :alt="getAnimeTitle(anime)"
          class="w-full h-64 object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <div class="absolute bottom-4 left-4 text-white">
          <h1 class="text-3xl font-bold">{{ getAnimeTitle(anime) }}</h1>
          <p v-if="anime.title_native && anime.title_native !== getAnimeTitle(anime)" 
             class="text-lg opacity-90">{{ anime.title_native }}</p>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column - Cover and Basic Info -->
        <div class="lg:col-span-1">
          <!-- Cover Image -->
          <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <img
              v-if="anime.cover_image_large || anime.cover_image?.large"
              :src="anime.cover_image_large || anime.cover_image?.large"
              :alt="getAnimeTitle(anime)"
              class="w-full h-auto object-cover"
            />
            <div v-else class="w-full h-96 bg-gray-300 flex items-center justify-center">
              <span class="text-gray-500">No Image Available</span>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">Quick Stats</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600">Score</span>
                <span class="font-medium">{{ anime.average_score ? `${anime.average_score}/100` : 'N/A' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Popularity</span>
                <span class="font-medium">{{ anime.popularity?.toLocaleString() || 'N/A' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Status</span>
                <span class="font-medium">{{ anime.status || 'Unknown' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Episodes</span>
                <span class="font-medium">{{ anime.episodes || 'N/A' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Duration</span>
                <span class="font-medium">{{ anime.duration ? `${anime.duration} min` : 'N/A' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Format</span>
                <span class="font-medium">{{ anime.media_type || anime.format || 'N/A' }}</span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="space-y-3">
            <router-link to="/" class="w-full btn-secondary block text-center">
              Back to Search
            </router-link>
            <router-link
              to="/themes"
              class="w-full btn-outline block text-center"
            >
              Browse All Themes
            </router-link>
          </div>
        </div>

        <!-- Right Column - Detailed Information -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Title Information -->
          <div v-if="!anime.banner_image" class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ getAnimeTitle(anime) }}</h1>
            <div v-if="hasMultipleTitles" class="space-y-1 text-gray-600">
              <p v-if="anime.title_romaji && anime.title_romaji !== getAnimeTitle(anime)">
                <span class="font-medium">Romaji:</span> {{ anime.title_romaji }}
              </p>
              <p v-if="anime.title_native && anime.title_native !== getAnimeTitle(anime)">
                <span class="font-medium">Native:</span> {{ anime.title_native }}
              </p>
              <p v-if="anime.title_english && anime.title_english !== getAnimeTitle(anime)">
                <span class="font-medium">English:</span> {{ anime.title_english }}
              </p>
            </div>
          </div>

          <!-- Synopsis -->
          <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold mb-4">Synopsis</h3>
            <div class="prose max-w-none">
              <p class="text-gray-700 leading-relaxed whitespace-pre-line">
                {{ anime.synopsis || anime.description || 'No synopsis available.' }}
              </p>
            </div>
          </div>

          <!-- Genres -->
          <div v-if="anime.genres && anime.genres.length > 0" class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold mb-4">Genres</h3>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="genre in anime.genres"
                :key="genre"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800"
              >
                {{ genre }}
              </span>
            </div>
          </div>

          <!-- Tags (if available from metadata) -->
          <div v-if="metadata?.tags && metadata.tags.length > 0" class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold mb-4">Tags</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div
                v-for="tag in metadata.tags.slice(0, 20)"
                :key="tag.name"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <span class="font-medium text-gray-900">{{ tag.name }}</span>
                  <p v-if="tag.description" class="text-sm text-gray-600 mt-1">{{ tag.description }}</p>
                </div>
                <span v-if="tag.rank" class="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                  {{ tag.rank }}%
                </span>
              </div>
            </div>
          </div>

          <!-- Studios (if available from metadata) -->
          <div v-if="metadata?.studios && metadata.studios.length > 0" class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold mb-4">Studios</h3>
            <div class="space-y-2">
              <div
                v-for="studio in metadata.studios"
                :key="studio.name"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <span class="font-medium">{{ studio.name }}</span>
                <span v-if="studio.is_animation_studio" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  Animation Studio
                </span>
              </div>
            </div>
          </div>

          <!-- Air Dates -->
          <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold mb-4">Air Information</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <span class="text-gray-600">Season:</span>
                <span class="font-medium ml-2">{{ anime.season || 'N/A' }} {{ anime.season_year || '' }}</span>
              </div>
              <div>
                <span class="text-gray-600">Source:</span>
                <span class="font-medium ml-2">{{ anime.source || 'N/A' }}</span>
              </div>
              <div v-if="anime.start_date">
                <span class="text-gray-600">Start Date:</span>
                <span class="font-medium ml-2">{{ formatDate(anime.start_date) }}</span>
              </div>
              <div v-if="anime.end_date">
                <span class="text-gray-600">End Date:</span>
                <span class="font-medium ml-2">{{ formatDate(anime.end_date) }}</span>
              </div>
            </div>
          </div>

          <!-- Theme Analysis -->
          <AnimeThemeDisplay
            v-if="anime"
            :anime="anime"
            :anime-id="props.id"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAnimeStore } from '@/stores/anime'
import type { AnimeSearchResult } from '@/types/theme'
import AnimeThemeDisplay from '@/components/AnimeThemeDisplay.vue'

const route = useRoute()
const router = useRouter()
const animeStore = useAnimeStore()

// Props
const props = defineProps<{
  id: string
}>()

// State
const anime = ref<AnimeSearchResult | null>(null)
const metadata = ref<any>(null)

// Computed
const hasMultipleTitles = computed(() => {
  if (!anime.value) return false
  const titles = [anime.value.title_english, anime.value.title_romaji, anime.value.title_native].filter(Boolean)
  return titles.length > 1
})

// Methods
function getAnimeTitle(anime: AnimeSearchResult): string {
  return anime.title_english || anime.title_romaji || anime.title_native || 'Unknown Title'
}

function formatDate(dateObj: any): string {
  if (!dateObj) return 'N/A'
  
  if (typeof dateObj === 'string') {
    return new Date(dateObj).toLocaleDateString()
  }
  
  if (dateObj.year) {
    const parts = []
    if (dateObj.month) parts.push(dateObj.month.toString().padStart(2, '0'))
    if (dateObj.day) parts.push(dateObj.day.toString().padStart(2, '0'))
    parts.unshift(dateObj.year.toString())
    return parts.join('-')
  }
  
  return 'N/A'
}

async function loadAnime() {
  try {
    // First try to get the anime details
    anime.value = await animeStore.getAnime(props.id)
    
    // Then try to get additional metadata
    try {
      metadata.value = await animeStore.getAnimeMetadata(props.id)
    } catch (metadataError) {
      console.warn('Could not load metadata:', metadataError)
      // Continue without metadata - it's optional
    }
  } catch (error) {
    console.error('Failed to load anime:', error)
  }
}



// Lifecycle
onMounted(() => {
  loadAnime()
})
</script>

<style scoped>
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
}
</style>
