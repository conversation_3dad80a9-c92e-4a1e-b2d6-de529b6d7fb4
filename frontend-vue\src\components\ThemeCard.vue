<template>
  <div
    class="theme-card bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
    @click="$emit('click')"
  >
    <!-- Header -->
    <div class="p-4 border-b border-gray-100">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
            {{ theme.name }}
          </h3>
          <div class="flex items-center mt-1 space-x-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getCategoryBadgeClass(theme.category)">
              {{ formatCategory(theme.category) }}
            </span>
            <span v-if="theme.confidence" class="text-xs text-gray-500">
              {{ Math.round(theme.confidence * 100) }}% match
            </span>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            @click.stop="$emit('view-cross-media')"
            class="p-1.5 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
            title="View cross-media mappings"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-4">
      <!-- Description -->
      <p class="text-sm text-gray-600 mb-3 line-clamp-2">
        {{ theme.description || theme.llm_context || 'No description available.' }}
      </p>

      <!-- Media Types -->
      <div v-if="theme.media_types && theme.media_types.length > 0" class="mb-3">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="mediaType in theme.media_types"
            :key="mediaType"
            class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
            :class="getMediaTypeBadgeClass(mediaType)"
          >
            {{ mediaType }}
          </span>
        </div>
      </div>

      <!-- Examples -->
      <div v-if="theme.examples && theme.examples.length > 0" class="mb-3">
        <h4 class="text-xs font-medium text-gray-700 mb-1">Examples:</h4>
        <div class="flex flex-wrap gap-1">
          <span
            v-for="example in theme.examples.slice(0, 3)"
            :key="example"
            class="inline-block px-2 py-1 bg-gray-100 text-xs text-gray-700 rounded-md"
          >
            {{ example }}
          </span>
          <span
            v-if="theme.examples.length > 3"
            class="inline-block px-2 py-1 bg-gray-100 text-xs text-gray-500 rounded-md"
          >
            +{{ theme.examples.length - 3 }} more
          </span>
        </div>
      </div>

      <!-- Cross-Media Indicator -->
      <div v-if="hasCrossMediaMappings" class="flex items-center text-xs text-primary-600">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
        </svg>
        Cross-media connections available
      </div>
    </div>

    <!-- Footer -->
    <div class="px-4 py-3 bg-gray-50 border-t border-gray-100 rounded-b-lg">
      <div class="flex items-center justify-between text-xs text-gray-500">
        <span>Theme ID: {{ theme.id.split('-')[0] }}...</span>
        <button
          @click.stop="$emit('click')"
          class="text-primary-600 hover:text-primary-700 font-medium"
        >
          View Details →
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Theme, ThemeCategory, MediaType } from '@/types/theme'

// Props
interface Props {
  theme: Theme
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  click: []
  'view-cross-media': []
}>()

// Computed
const hasCrossMediaMappings = computed(() => {
  return props.theme.cross_media_mappings && 
         Object.keys(props.theme.cross_media_mappings).length > 0
})

// Methods
function getCategoryBadgeClass(category: ThemeCategory): string {
  const classes = {
    MOOD: 'bg-purple-100 text-purple-800',
    NARRATIVE_STRUCTURE: 'bg-blue-100 text-blue-800',
    CHARACTER_DYNAMIC: 'bg-green-100 text-green-800',
    SETTING_TYPE: 'bg-orange-100 text-orange-800',
    ANIME_SPECIFIC: 'bg-pink-100 text-pink-800',
    TV_SPECIFIC: 'bg-indigo-100 text-indigo-800',
    BOOK_SPECIFIC: 'bg-yellow-100 text-yellow-800',
    MUSIC_SPECIFIC: 'bg-red-100 text-red-800',
    CULTURAL_CONTEXT: 'bg-teal-100 text-teal-800',
    ARTISTIC_STYLE: 'bg-cyan-100 text-cyan-800',
    THEMATIC_DEPTH: 'bg-gray-100 text-gray-800',
    AUDIENCE_APPEAL: 'bg-emerald-100 text-emerald-800',
  }
  return classes[category] || 'bg-gray-100 text-gray-800'
}

function getMediaTypeBadgeClass(mediaType: MediaType): string {
  const classes = {
    ANIME: 'bg-pink-50 text-pink-700 border border-pink-200',
    TV: 'bg-blue-50 text-blue-700 border border-blue-200',
    BOOK: 'bg-yellow-50 text-yellow-700 border border-yellow-200',
    MUSIC: 'bg-red-50 text-red-700 border border-red-200',
    MOVIE: 'bg-purple-50 text-purple-700 border border-purple-200',
    GAME: 'bg-green-50 text-green-700 border border-green-200',
  }
  return classes[mediaType] || 'bg-gray-50 text-gray-700 border border-gray-200'
}

function formatCategory(category: ThemeCategory): string {
  return category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.theme-card:hover {
  transform: translateY(-1px);
}
</style>
