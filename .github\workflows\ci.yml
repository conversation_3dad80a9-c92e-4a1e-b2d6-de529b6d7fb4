name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, mvp-vue-architecture ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      neo4j:
        image: neo4j:5-community
        env:
          NEO4J_AUTH: neo4j/password
          NEO4J_dbms_memory_pagecache_size: 512M
          NEO4J_dbms.memory.heap.initial_size: 512M
          NEO4J_dbms_memory_heap_max__size: 1G
        ports:
          - 7474:7474
          - 7687:7687
        options: >-
          --health-cmd "cypher-shell -u neo4j -p password 'RETURN 1'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install Python dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov
    
    - name: Wait for services
      run: |
        sleep 10
        curl -f http://localhost:7474 || exit 1
        redis-cli -h localhost ping || exit 1
    
    - name: Run backend tests
      run: |
        cd backend
        python -m pytest tests/ -v --cov=app --cov-report=xml --cov-report=html
      env:
        NEO4J_URI: bolt://localhost:7687
        NEO4J_USER: neo4j
        NEO4J_PASSWORD: password
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        REDIS_DB: 1
    
    - name: Upload backend coverage
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        flags: backend
        name: backend-coverage

  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend-vue/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend-vue
        npm ci
    
    - name: Run frontend linting
      run: |
        cd frontend-vue
        npm run lint
    
    - name: Run frontend tests
      run: |
        cd frontend-vue
        npm run test:coverage
    
    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        file: frontend-vue/coverage/coverage-final.json
        flags: frontend
        name: frontend-coverage

  docker-build:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build backend image
      run: |
        docker build -f backend/Dockerfile.dev -t tahimoto-backend:test backend/
    
    - name: Build frontend image
      run: |
        docker build -f frontend-vue/Dockerfile.dev -t tahimoto-frontend:test frontend-vue/
    
    - name: Test Docker Compose
      run: |
        docker-compose -f docker-compose.yml config

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [docker-build]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Compose
      run: |
        docker-compose up -d --build
        sleep 30
    
    - name: Wait for services
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:8000/health; do sleep 2; done'
        timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'
    
    - name: Run integration tests
      run: |
        # Test API endpoints
        curl -f http://localhost:8000/api/v1/stories/search?q=test
        curl -f http://localhost:8000/graphql -X POST -H "Content-Type: application/json" -d '{"query":"query { __schema { types { name } } }"}'
    
    - name: Cleanup
      if: always()
      run: |
        docker-compose down -v

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, docker-build, integration-tests]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Staging deployment would happen here"
        # Add actual deployment steps when ready

    - name: Notify deployment
      run: |
        echo "Staging deployment completed"

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, docker-build, integration-tests, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Production deployment would happen here"
        # Add actual deployment steps when ready

    - name: Notify deployment
      run: |
        echo "Production deployment completed"
