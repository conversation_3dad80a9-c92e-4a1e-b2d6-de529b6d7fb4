<template>
  <div
    v-if="show"
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
  >
    <!-- Background overlay -->
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        aria-hidden="true"
        @click="$emit('close')"
      ></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Header -->
        <div class="bg-white px-6 py-4 border-b border-gray-200">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-2xl font-bold text-gray-900" id="modal-title">
                {{ theme.name }}
              </h3>
              <div class="flex items-center mt-2 space-x-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                      :class="getCategoryBadgeClass(theme.category)">
                  {{ formatCategory(theme.category) }}
                </span>
                <span v-if="theme.confidence" class="text-sm text-gray-500">
                  {{ Math.round(theme.confidence * 100) }}% confidence
                </span>
              </div>
            </div>
            <button
              @click="$emit('close')"
              class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="bg-white px-6 py-6 max-h-96 overflow-y-auto">
          <!-- Description -->
          <div class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Description</h4>
            <p class="text-gray-700 leading-relaxed">
              {{ theme.description || 'No description available.' }}
            </p>
          </div>

          <!-- LLM Context -->
          <div v-if="theme.llm_context" class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Context & Usage</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <p class="text-gray-700 text-sm leading-relaxed">
                {{ theme.llm_context }}
              </p>
            </div>
          </div>

          <!-- Media Types -->
          <div v-if="theme.media_types && theme.media_types.length > 0" class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Supported Media Types</h4>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="mediaType in theme.media_types"
                :key="mediaType"
                class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium"
                :class="getMediaTypeBadgeClass(mediaType)"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getMediaTypeIcon(mediaType)" />
                </svg>
                {{ mediaType }}
              </span>
            </div>
          </div>

          <!-- Examples -->
          <div v-if="theme.examples && theme.examples.length > 0" class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Examples</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <div
                v-for="example in theme.examples"
                :key="example"
                class="bg-gray-50 rounded-md px-3 py-2 text-sm text-gray-700"
              >
                {{ example }}
              </div>
            </div>
          </div>

          <!-- Cross-Media Mappings -->
          <div v-if="hasCrossMediaMappings" class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Cross-Media Connections</h4>
            <div class="space-y-3">
              <div
                v-for="(mapping, mediaType) in theme.cross_media_mappings"
                :key="mediaType"
                class="border border-gray-200 rounded-lg p-4"
              >
                <div class="flex items-center mb-2">
                  <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium"
                        :class="getMediaTypeBadgeClass(mediaType as any)">
                    {{ mediaType.toUpperCase() }}
                  </span>
                </div>
                <div class="text-sm text-gray-600">
                  <div v-if="mapping.genres && mapping.genres.length > 0" class="mb-2">
                    <span class="font-medium">Genres:</span>
                    {{ mapping.genres.join(', ') }}
                  </div>
                  <div v-if="mapping.tags && mapping.tags.length > 0" class="mb-2">
                    <span class="font-medium">Tags:</span>
                    {{ mapping.tags.join(', ') }}
                  </div>
                  <div v-if="mapping.examples && mapping.examples.length > 0">
                    <span class="font-medium">Examples:</span>
                    {{ mapping.examples.join(', ') }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Technical Details -->
          <div class="border-t border-gray-200 pt-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Technical Details</h4>
            <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <dt class="font-medium text-gray-700">Theme ID</dt>
                <dd class="text-gray-600 font-mono text-xs">{{ theme.id }}</dd>
              </div>
              <div>
                <dt class="font-medium text-gray-700">Category</dt>
                <dd class="text-gray-600">{{ formatCategory(theme.category) }}</dd>
              </div>
              <div v-if="theme.confidence">
                <dt class="font-medium text-gray-700">Confidence Score</dt>
                <dd class="text-gray-600">{{ theme.confidence.toFixed(3) }}</dd>
              </div>
              <div>
                <dt class="font-medium text-gray-700">Media Support</dt>
                <dd class="text-gray-600">{{ theme.media_types?.length || 0 }} media types</dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex items-center justify-between">
          <div class="flex space-x-3">
            <button
              @click="$emit('view-related', theme)"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              View Related Themes
            </button>
          </div>
          <button
            @click="$emit('close')"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Theme, ThemeCategory, MediaType } from '@/types/theme'

// Props
interface Props {
  theme: Theme
  show: boolean
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  close: []
  'view-related': [theme: Theme]
}>()

// Computed
const hasCrossMediaMappings = computed(() => {
  return props.theme.cross_media_mappings && 
         Object.keys(props.theme.cross_media_mappings).length > 0
})

// Methods
function getCategoryBadgeClass(category: ThemeCategory): string {
  const classes = {
    MOOD: 'bg-purple-100 text-purple-800',
    NARRATIVE_STRUCTURE: 'bg-blue-100 text-blue-800',
    CHARACTER_DYNAMIC: 'bg-green-100 text-green-800',
    SETTING_TYPE: 'bg-orange-100 text-orange-800',
    ANIME_SPECIFIC: 'bg-pink-100 text-pink-800',
    TV_SPECIFIC: 'bg-indigo-100 text-indigo-800',
    BOOK_SPECIFIC: 'bg-yellow-100 text-yellow-800',
    MUSIC_SPECIFIC: 'bg-red-100 text-red-800',
    CULTURAL_CONTEXT: 'bg-teal-100 text-teal-800',
    ARTISTIC_STYLE: 'bg-cyan-100 text-cyan-800',
    THEMATIC_DEPTH: 'bg-gray-100 text-gray-800',
    AUDIENCE_APPEAL: 'bg-emerald-100 text-emerald-800',
  }
  return classes[category] || 'bg-gray-100 text-gray-800'
}

function getMediaTypeBadgeClass(mediaType: MediaType): string {
  const classes = {
    ANIME: 'bg-pink-50 text-pink-700 border border-pink-200',
    TV: 'bg-blue-50 text-blue-700 border border-blue-200',
    BOOK: 'bg-yellow-50 text-yellow-700 border border-yellow-200',
    MUSIC: 'bg-red-50 text-red-700 border border-red-200',
    MOVIE: 'bg-purple-50 text-purple-700 border border-purple-200',
    GAME: 'bg-green-50 text-green-700 border border-green-200',
  }
  return classes[mediaType] || 'bg-gray-50 text-gray-700 border border-gray-200'
}

function getMediaTypeIcon(mediaType: MediaType): string {
  const icons = {
    ANIME: 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h3a1 1 0 011 1v14a1 1 0 01-1 1H4a1 1 0 01-1-1V5a1 1 0 011-1h3z',
    TV: 'M21 3H3v18h18V3zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z',
    BOOK: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
    MUSIC: 'M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3',
    MOVIE: 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h3a1 1 0 011 1v14a1 1 0 01-1 1H4a1 1 0 01-1-1V5a1 1 0 011-1h3z',
    GAME: 'M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 000 1.788l4 2.016V9.236l-4-2.016V11z'
  }
  return icons[mediaType] || icons.ANIME
}

function formatCategory(category: ThemeCategory): string {
  return category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
}
</script>
