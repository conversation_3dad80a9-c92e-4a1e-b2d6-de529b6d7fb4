<template>
  <div class="theme-browser bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Cross-Media Themes</h2>
          <p class="text-sm text-gray-600 mt-1">
            Explore themes that connect across anime, TV, books, and music
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-500">
            {{ filteredThemes.length }} themes
          </span>
          <button
            @click="refreshThemes"
            :disabled="loading"
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <svg class="w-4 h-4 mr-1.5" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
      <div class="flex flex-wrap items-center gap-4">
        <!-- Category Filter -->
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Category:</label>
          <select
            v-model="selectedCategory"
            class="block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            <option value="">All Categories</option>
            <option value="MOOD">Mood</option>
            <option value="NARRATIVE_STRUCTURE">Narrative Structure</option>
            <option value="CHARACTER_DYNAMIC">Character Dynamic</option>
            <option value="SETTING_TYPE">Setting Type</option>
          </select>
        </div>

        <!-- Media Type Filter -->
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Media:</label>
          <select
            v-model="selectedMediaType"
            class="block w-32 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            <option value="">All Media</option>
            <option value="ANIME">Anime</option>
            <option value="TV">TV</option>
            <option value="BOOK">Book</option>
            <option value="MUSIC">Music</option>
          </select>
        </div>

        <!-- Search -->
        <div class="flex-1 max-w-md">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search themes..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading && themes.length === 0" class="px-6 py-12 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading themes...
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="px-6 py-12 text-center">
      <div class="text-red-600">
        <svg class="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <p class="text-sm">{{ error }}</p>
        <button
          @click="refreshThemes"
          class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Try Again
        </button>
      </div>
    </div>

    <!-- Themes Grid -->
    <div v-else-if="filteredThemes.length > 0" class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <ThemeCard
          v-for="theme in filteredThemes"
          :key="theme.id"
          :theme="theme"
          @click="selectTheme(theme)"
          @view-cross-media="viewCrossMediaMappings(theme)"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="px-6 py-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No themes found</h3>
      <p class="text-gray-500 mb-4">
        {{ searchQuery || selectedCategory || selectedMediaType 
           ? 'Try adjusting your filters or search terms.' 
           : 'No themes are available yet.' }}
      </p>
      <button
        v-if="!searchQuery && !selectedCategory && !selectedMediaType"
        @click="refreshThemes"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        Load Themes
      </button>
    </div>

    <!-- Theme Detail Modal -->
    <ThemeDetailModal
      v-if="selectedTheme"
      :theme="selectedTheme"
      :show="showThemeDetail"
      @close="closeThemeDetail"
      @view-related="viewRelatedThemes"
    />

    <!-- Cross-Media Modal -->
    <CrossMediaModal
      v-if="crossMediaTheme"
      :theme="crossMediaTheme"
      :show="showCrossMediaModal"
      @close="closeCrossMediaModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { themeMappingApi } from '@/services/api'
import type { Theme } from '@/types/theme'
import ThemeCard from './ThemeCard.vue'
import ThemeDetailModal from './ThemeDetailModal.vue'
import CrossMediaModal from './CrossMediaModal.vue'

// Props
interface Props {
  initialCategory?: string
  initialMediaType?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialCategory: '',
  initialMediaType: ''
})

// Emits
const emit = defineEmits<{
  themeSelected: [theme: Theme]
  categoryChanged: [category: string]
}>()

// State
const themes = ref<Theme[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// Filters
const selectedCategory = ref(props.initialCategory)
const selectedMediaType = ref(props.initialMediaType)
const searchQuery = ref('')

// Modals
const selectedTheme = ref<Theme | null>(null)
const showThemeDetail = ref(false)
const crossMediaTheme = ref<Theme | null>(null)
const showCrossMediaModal = ref(false)

// Computed
const filteredThemes = computed(() => {
  let filtered = themes.value

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter(theme => theme.category === selectedCategory.value)
  }

  // Filter by media type
  if (selectedMediaType.value) {
    filtered = filtered.filter(theme => 
      theme.media_types?.includes(selectedMediaType.value as any)
    )
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(theme =>
      theme.name.toLowerCase().includes(query) ||
      theme.description?.toLowerCase().includes(query) ||
      theme.llm_context?.toLowerCase().includes(query)
    )
  }

  return filtered
})

// Methods
async function loadThemes() {
  try {
    loading.value = true
    error.value = null

    // Load themes from the database
    const themesData = await themeMappingApi.getAllThemes()

    // Transform the data to match our Theme interface
    themes.value = themesData.map((theme: any) => ({
      id: theme.id,
      name: theme.name,
      description: theme.description,
      category: theme.category,
      media_types: theme.media_types || [],
      examples: theme.examples || [],
      cross_media_mappings: theme.cross_media_mappings ?
        (typeof theme.cross_media_mappings === 'string' ?
          JSON.parse(theme.cross_media_mappings) :
          theme.cross_media_mappings) : {},
      llm_context: theme.llm_context
    }))
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load themes'
    console.error('Error loading themes:', err)
  } finally {
    loading.value = false
  }
}

async function refreshThemes() {
  await loadThemes()
}

function selectTheme(theme: Theme) {
  selectedTheme.value = theme
  showThemeDetail.value = true
  emit('themeSelected', theme)
}

function closeThemeDetail() {
  showThemeDetail.value = false
  selectedTheme.value = null
}

function viewCrossMediaMappings(theme: Theme) {
  crossMediaTheme.value = theme
  showCrossMediaModal.value = true
}

function closeCrossMediaModal() {
  showCrossMediaModal.value = false
  crossMediaTheme.value = null
}

function viewRelatedThemes(theme: Theme) {
  // Implementation for viewing related themes
  console.log('View related themes for:', theme.name)
}

// Watchers
watch([selectedCategory, selectedMediaType], () => {
  emit('categoryChanged', selectedCategory.value)
})

// Lifecycle
onMounted(() => {
  loadThemes()
})
</script>

<style scoped>
.theme-browser {
  /* Custom styles if needed */
}
</style>
