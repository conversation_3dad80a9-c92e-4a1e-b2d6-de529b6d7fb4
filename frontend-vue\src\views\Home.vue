<template>
  <div class="px-4 py-6 sm:px-0">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
        Welcome to Tahimoto
      </h1>
      <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
        Discover and explore themes across anime, TV shows, books, and music.
        Our advanced theme analysis helps you find connections between different media.
      </p>
      <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8 space-x-4">
        <div class="rounded-md shadow">
          <button
            @click="showAnimeSearch = !showAnimeSearch"
            class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"
          >
            {{ showAnimeSearch ? 'Hide' : 'Search' }} Anime
          </button>
        </div>
        <div class="rounded-md shadow">
          <router-link
            to="/admin"
            class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"
          >
            Admin Panel
          </router-link>
        </div>
      </div>
    </div>

    <!-- Anime Search Section -->
    <div v-if="showAnimeSearch" class="mb-12">
      <AnimeSearch @anime-selected="handleAnimeSelected" />
    </div>

    <!-- Selected Anime Analysis -->
    <div v-if="selectedAnime" class="mb-12">
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900">Selected Anime</h3>
          <p class="mt-1 text-sm text-gray-600">
            Ready for theme analysis - you can now analyze this anime in the admin panel
          </p>
        </div>
        <div class="card-body">
          <div class="flex items-start space-x-4">
            <img
              v-if="selectedAnime.cover_image_medium || selectedAnime.cover_image?.medium"
              :src="selectedAnime.cover_image_medium || selectedAnime.cover_image?.medium"
              :alt="getAnimeTitle(selectedAnime)"
              class="w-20 h-28 object-cover rounded-lg"
            />
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{{ getAnimeTitle(selectedAnime) }}</h4>
              <p class="text-sm text-gray-600 mt-1 line-clamp-3">
                {{ selectedAnime.synopsis || selectedAnime.description || 'No description available' }}
              </p>
              <div v-if="selectedAnime.genres && selectedAnime.genres.length > 0" class="mt-3 flex flex-wrap gap-2">
                <span
                  v-for="genre in selectedAnime.genres.slice(0, 5)"
                  :key="genre"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                >
                  {{ genre }}
                </span>
              </div>
              <div class="mt-4 flex space-x-3">
                <router-link
                  :to="`/admin/themes?anime=${selectedAnime.id}`"
                  class="btn-primary text-sm"
                >
                  Analyze Themes
                </router-link>
                <button
                  @click="clearSelection"
                  class="btn-secondary text-sm"
                >
                  Clear Selection
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-16">
      <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl mb-4">📺</div>
            <h3 class="text-lg font-medium text-gray-900">TV Shows</h3>
            <p class="mt-2 text-sm text-gray-500">
              Analyze themes in television series and episodes
            </p>
          </div>
        </div>
        
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl mb-4">📚</div>
            <h3 class="text-lg font-medium text-gray-900">Books</h3>
            <p class="mt-2 text-sm text-gray-500">
              Discover literary themes and narrative structures
            </p>
          </div>
        </div>
        
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl mb-4">🎌</div>
            <h3 class="text-lg font-medium text-gray-900">Anime</h3>
            <p class="mt-2 text-sm text-gray-500">
              Explore themes in anime series and movies
            </p>
          </div>
        </div>
        
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl mb-4">🎵</div>
            <h3 class="text-lg font-medium text-gray-900">Music</h3>
            <p class="mt-2 text-sm text-gray-500">
              Find thematic connections in musical works
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAnimeStore } from '@/stores/anime'
import AnimeSearch from '@/components/AnimeSearch.vue'
import type { AnimeSearchResult } from '@/types/theme'

const animeStore = useAnimeStore()

// State
const showAnimeSearch = ref(false)
const selectedAnime = ref<AnimeSearchResult | null>(null)

// Methods
function getAnimeTitle(anime: AnimeSearchResult): string {
  return anime.title_english || anime.title_romaji || anime.title_native || 'Unknown Title'
}

function handleAnimeSelected(anime: AnimeSearchResult) {
  selectedAnime.value = anime
  showAnimeSearch.value = false // Hide search after selection
}

function clearSelection() {
  selectedAnime.value = null
  animeStore.clearSelection()
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
