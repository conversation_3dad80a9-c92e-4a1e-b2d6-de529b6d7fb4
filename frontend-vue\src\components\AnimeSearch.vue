<template>
  <div class="anime-search">
    <!-- Search Form -->
    <div class="card mb-6">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Search Anime</h3>
        <p class="mt-1 text-sm text-gray-600">
          Find anime to analyze themes and discover connections
        </p>
      </div>
      <div class="card-body">
        <form @submit.prevent="handleSearch" class="space-y-4">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div class="sm:col-span-2">
              <label for="search-query" class="block text-sm font-medium text-gray-700">
                Search Query
              </label>
              <input
                id="search-query"
                v-model="searchQuery"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Enter anime title..."
              />
            </div>
            <div>
              <label for="sort-by" class="block text-sm font-medium text-gray-700">
                Sort By
              </label>
              <select
                id="sort-by"
                v-model="sortBy"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="popularity">Popularity</option>
                <option value="score">Score</option>
                <option value="title">Title</option>
                <option value="newest">Newest</option>
              </select>
            </div>
          </div>
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-500">
              Results per page: {{ perPage }}
            </div>
            <button
              type="submit"
              :disabled="animeStore.loading || !searchQuery.trim()"
              class="btn-primary"
            >
              <span v-if="animeStore.loading">Searching...</span>
              <span v-else>Search Anime</span>
            </button>
          </div>
        </form>
      </div>
    </div>



    <!-- Error Display -->
    <div v-if="animeStore.hasError" class="mb-6">
      <div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-2 text-sm text-red-700">
              {{ animeStore.error }}
            </div>
            <div class="mt-4">
              <button
                @click="animeStore.clearError"
                class="text-sm bg-red-100 text-red-800 hover:bg-red-200 px-3 py-1 rounded-md"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div v-if="animeStore.hasResults" class="space-y-6">
      <!-- Results Header -->
      <div class="flex justify-between items-center">
        <div>
          <h3 class="text-lg font-medium text-gray-900">
            Search Results ({{ animeStore.totalResults.toLocaleString() }})
          </h3>
          <p class="text-sm text-gray-600">
            Page {{ animeStore.currentPage }} of {{ animeStore.totalPages }}
          </p>
        </div>
        <div class="flex space-x-2">
          <button
            @click="previousPage"
            :disabled="animeStore.currentPage <= 1 || animeStore.loading"
            class="btn-secondary text-sm"
          >
            Previous
          </button>
          <button
            @click="nextPage"
            :disabled="animeStore.currentPage >= animeStore.totalPages || animeStore.loading"
            class="btn-secondary text-sm"
          >
            Next
          </button>
        </div>
      </div>

      <!-- Results Grid -->
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <div
          v-for="anime in animeStore.searchResults?.items"
          :key="anime.id"
          class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer"
          @click="selectAnime(anime)"
        >
          <!-- Cover Image -->
          <div class="aspect-w-3 aspect-h-4 bg-gray-200">
            <img
              v-if="anime.cover_image_large || anime.cover_image?.large"
              :src="anime.cover_image_large || anime.cover_image?.large"
              :alt="getAnimeTitle(anime)"
              class="w-full h-48 object-cover"
            />
            <div v-else class="w-full h-48 bg-gray-300 flex items-center justify-center">
              <span class="text-gray-500">No Image</span>
            </div>
          </div>
          
          <!-- Content -->
          <div class="p-4">
            <h4 class="font-medium text-gray-900 line-clamp-2">
              {{ getAnimeTitle(anime) }}
            </h4>
            <p class="text-sm text-gray-600 mt-1 line-clamp-3">
              {{ anime.synopsis || anime.description || 'No description available' }}
            </p>
            
            <!-- Metadata -->
            <div class="mt-3 space-y-2">
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>{{ anime.media_type || anime.format || 'Unknown' }}</span>
                <span v-if="anime.episodes">{{ anime.episodes }} episodes</span>
              </div>
              
              <div v-if="anime.average_score" class="flex items-center">
                <span class="text-xs text-gray-500 mr-2">Score:</span>
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-primary-600 h-2 rounded-full"
                    :style="{ width: `${anime.average_score}%` }"
                  ></div>
                </div>
                <span class="text-xs text-gray-500 ml-2">{{ anime.average_score }}/100</span>
              </div>
              
              <!-- Genres -->
              <div v-if="anime.genres && anime.genres.length > 0" class="flex flex-wrap gap-1">
                <span
                  v-for="genre in anime.genres.slice(0, 3)"
                  :key="genre"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                >
                  {{ genre }}
                </span>
                <span
                  v-if="anime.genres.length > 3"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  +{{ anime.genres.length - 3 }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!animeStore.loading && searchQuery" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No results found</h3>
      <p class="mt-1 text-sm text-gray-500">Try adjusting your search terms.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAnimeStore } from '@/stores/anime'
import type { AnimeSearchResult } from '@/types/theme'

const router = useRouter()
const animeStore = useAnimeStore()

// Emit events
const emit = defineEmits<{
  animeSelected: [anime: AnimeSearchResult]
}>()

// Form data
const searchQuery = ref('')
const sortBy = ref('popularity')
const perPage = ref(12)
const currentPage = ref(1)

// Methods
function getAnimeTitle(anime: AnimeSearchResult): string {
  return anime.title_english || anime.title_romaji || anime.title_native || 'Unknown Title'
}

async function handleSearch() {
  if (!searchQuery.value.trim()) return
  
  currentPage.value = 1
  await performSearch()
}

async function performSearch() {
  try {
    await animeStore.searchAnime({
      query: searchQuery.value,
      page: currentPage.value,
      per_page: perPage.value,
      sort: sortBy.value,
    })
  } catch (error) {
    console.error('Search failed:', error)
  }
}

async function nextPage() {
  if (currentPage.value < animeStore.totalPages) {
    currentPage.value++
    await performSearch()
  }
}

async function previousPage() {
  if (currentPage.value > 1) {
    currentPage.value--
    await performSearch()
  }
}

function selectAnime(anime: AnimeSearchResult) {
  animeStore.selectAnime(anime)
  emit('animeSelected', anime)

  // Navigate to anime detail page
  router.push({
    name: 'AnimeDetail',
    params: { id: anime.id }
  })
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
