#!/usr/bin/env python
"""
Initialize core themes based on the theme taxonomy document.
This script populates the database with the initial set of themes categorized
by MOOD, NARRATIVE_STRUCTURE, CHARACTER_DYNAMIC, and SETTING_TYPE.
"""
import asyncio
import os
import sys
import uuid
from datetime import datetime
from pathlib import Path

# Add the parent directory to the sys.path for imports to work
sys.path.append(str(Path(__file__).parent.parent))

from app.db.neo4j_session import driver
from app.schemas.theme_category import ThemeCategory
from app.schemas.theme import ThemeStatus

# Define core themes organized by category
CORE_THEMES = {
    ThemeCategory.MOOD: [
        {
            "name": "Cozy",
            "description": "Warm, comfortable, safe feelings that create a sense of security and contentment."
        },
        {
            "name": "Tense",
            "description": "Anxiety-inducing, on-edge atmosphere that creates suspense and unease."
        },
        {
            "name": "Melancholic",
            "description": "Wistful, reflective, tinged with sadness and contemplative emotions."
        },
        {
            "name": "Uplifting",
            "description": "Inspiring, positive emotional response that elevates the spirit."
        },
        {
            "name": "Horrific",
            "description": "Fear-inducing, disturbing content that creates dread and terror."
        },
        {
            "name": "Comedic",
            "description": "Humorous, light-hearted tone designed to amuse and entertain."
        },
        {
            "name": "Romantic",
            "description": "Love-focused, heartwarming emotional tone centered on relationships."
        }
    ],
    ThemeCategory.NARRATIVE_STRUCTURE: [
        {
            "name": "Journey",
            "description": "Physical and/or metaphorical travel where characters progress through changing environments."
        },
        {
            "name": "Coming of Age",
            "description": "Evolution from youth to maturity focusing on character growth and self-discovery."
        },
        {
            "name": "Mystery",
            "description": "Incremental revelation of hidden information driving the narrative forward."
        },
        {
            "name": "Episodic",
            "description": "Self-contained episodes with loose continuity between segments."
        },
        {
            "name": "Tournament",
            "description": "Structured competition with escalating challenges and defined rules."
        },
        {
            "name": "Ensemble",
            "description": "Multiple character arcs of similar importance interweaving throughout the story."
        },
        {
            "name": "Tragedy",
            "description": "Inevitable downfall of characters despite their efforts to prevent it."
        }
    ],
    ThemeCategory.CHARACTER_DYNAMIC: [
        {
            "name": "Found Family",
            "description": "Non-blood relations forming familial bonds and support structures."
        },
        {
            "name": "Rivalry",
            "description": "Competitive relationship driving mutual growth and development."
        },
        {
            "name": "Mentor-Student",
            "description": "Knowledge and wisdom transfer relationship with clear hierarchy."
        },
        {
            "name": "Romance",
            "description": "Romantic relationship development with emotional and/or physical attraction."
        },
        {
            "name": "Ensemble Cast",
            "description": "Group dynamics with multiple key relationships and interactions."
        },
        {
            "name": "Lone Wolf",
            "description": "Character operating independently despite social contexts and pressures."
        },
        {
            "name": "Hero's Journey Companions",
            "description": "Supporting characters aiding protagonist's growth and adventure."
        }
    ],
    ThemeCategory.SETTING_TYPE: [
        {
            "name": "School Life",
            "description": "Educational institution as primary setting with academic activities."
        },
        {
            "name": "Fantasy World",
            "description": "Imaginary setting with its own rules, often including magical elements."
        },
        {
            "name": "Historical",
            "description": "Based in real historical period with period-appropriate details."
        },
        {
            "name": "Urban Modern",
            "description": "Contemporary city setting with modern technology and social structures."
        },
        {
            "name": "Post-Apocalyptic",
            "description": "Setting after societal collapse with survival challenges."
        },
        {
            "name": "Workplace",
            "description": "Professional environment focus with career-related activities."
        },
        {
            "name": "Rural",
            "description": "Countryside or small town setting with nature prominence."
        }
    ]
}

# Define theme relationships
THEME_RELATIONSHIPS = [
    # Complementary relationships
    {
        "source": "Cozy",
        "target": "Rural",
        "type": "COMPLEMENTS",
        "properties": {"strength": 0.9}
    },
    {
        "source": "Tense",
        "target": "Mystery",
        "type": "COMPLEMENTS",
        "properties": {"strength": 0.8}
    },
    {
        "source": "School Life",
        "target": "Coming of Age",
        "type": "COMPLEMENTS",
        "properties": {"strength": 0.9}
    },
    # Contrasting relationships
    {
        "source": "Comedic",
        "target": "Tragedy",
        "type": "CONTRASTS",
        "properties": {"tension_value": 0.7}
    },
    {
        "source": "Cozy",
        "target": "Horrific",
        "type": "CONTRASTS",
        "properties": {"tension_value": 0.9}
    }
]

async def create_theme_direct(session, theme_data, category):
    """Create a theme directly using a Cypher query."""
    theme_id = f"theme_{uuid.uuid4()}"
    now = datetime.now().isoformat()
    
    query = """
    CREATE (t:Theme {
        id: $id,
        name: $name,
        description: $description,
        category: $category,
        status: $status,
        confidence: $confidence,
        created_at: $created_at,
        updated_at: $updated_at
    })
    RETURN t
    """
    
    params = {
        "id": theme_id,
        "name": theme_data["name"],
        "description": theme_data["description"],
        "category": category,
        "status": "MAPPED",
        "confidence": 1.0,
        "created_at": now,
        "updated_at": now
    }
    
    try:
        result = await session.run(query, params)
        record = await result.single()
        if record:
            print(f"Created theme: {theme_data['name']} ({theme_id})")
            return theme_id
        else:
            print(f"Error creating theme: {theme_data['name']}")
            return None
    except Exception as e:
        print(f"Error creating theme {theme_data['name']}: {str(e)}")
        return None

async def create_theme_relationship_direct(session, source_id, target_id, rel_type, properties=None):
    """Create a relationship between themes directly with Cypher query."""
    if properties is None:
        properties = {}

    # Add timestamp and ID to relationship
    relationship_id = f"rel_{uuid.uuid4()}"
    now = datetime.now().isoformat()
    properties.update({
        "id": relationship_id,
        "created_at": now,
        "updated_at": now
    })

    props_list = []
    params = {
        "source_id": source_id,
        "target_id": target_id
    }

    for key, value in properties.items():
        prop_name = f"prop_{key}"
        props_list.append(f"{key}: ${prop_name}")
        params[prop_name] = value

    props_str = "{" + ", ".join(props_list) + "}" if props_list else ""

    query = f"""
    MATCH (source:Theme {{id: $source_id}})
    MATCH (target:Theme {{id: $target_id}})
    MERGE (source)-[r:{rel_type} {props_str}]->(target)
    RETURN source, r, target
    """

    try:
        result = await session.run(query, params)
        record = await result.single()

        if not record:
            print(f"Failed to create {rel_type} relationship between {source_id} and {target_id}")
            return None

        return {
            "source": dict(record["source"]),
            "relationship": dict(record["r"]),
            "target": dict(record["target"])
        }
    except Exception as e:
        print(f"Error creating relationship {rel_type}: {str(e)}")
        return None

async def create_themes():
    """Create core themes and their relationships."""
    async with driver.session() as session:
        # Dictionary to store created themes by name for relationship creation
        created_themes = {}
        
        # Create themes for each category
        print("Creating themes for category: ThemeCategory.MOOD")
        for theme_data in CORE_THEMES[ThemeCategory.MOOD]:
            theme_id = await create_theme_direct(session, theme_data, "MOOD")
            if theme_id:
                created_themes[theme_data['name']] = theme_id
        
        print("Creating themes for category: ThemeCategory.NARRATIVE_STRUCTURE")
        for theme_data in CORE_THEMES[ThemeCategory.NARRATIVE_STRUCTURE]:
            theme_id = await create_theme_direct(session, theme_data, "NARRATIVE_STRUCTURE")
            if theme_id:
                created_themes[theme_data['name']] = theme_id
        
        print("Creating themes for category: ThemeCategory.CHARACTER_DYNAMIC")
        for theme_data in CORE_THEMES[ThemeCategory.CHARACTER_DYNAMIC]:
            theme_id = await create_theme_direct(session, theme_data, "CHARACTER_DYNAMIC")
            if theme_id:
                created_themes[theme_data['name']] = theme_id
        
        print("Creating themes for category: ThemeCategory.SETTING_TYPE")
        for theme_data in CORE_THEMES[ThemeCategory.SETTING_TYPE]:
            theme_id = await create_theme_direct(session, theme_data, "SETTING_TYPE")
            if theme_id:
                created_themes[theme_data['name']] = theme_id
        
        # Create relationships between themes
        print("\nCreating theme relationships...")
        for rel in THEME_RELATIONSHIPS:
            source_id = created_themes.get(rel['source'])
            target_id = created_themes.get(rel['target'])
            
            if not source_id or not target_id:
                print(f"Cannot create relationship: missing theme {rel['source']} or {rel['target']}")
                continue
                
            try:
                # Create the relationship directly
                relationship = await create_theme_relationship_direct(
                    session,
                    source_id,
                    target_id,
                    rel['type'],
                    rel.get('properties', {})
                )
                print(f"Created relationship: {rel['source']} -{rel['type']}-> {rel['target']}")
            except Exception as e:
                print(f"Error creating relationship: {str(e)}")
                
        print("\nCore themes initialization complete!")

if __name__ == "__main__":
    asyncio.run(create_themes()) 