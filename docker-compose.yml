services:
  frontend:
    container_name: tahimoto-frontend
    build:
      context: ./frontend-vue
      dockerfile: Dockerfile.dev
    volumes:
      - ./frontend-vue:/app
      - /app/node_modules
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:${BACKEND_PORT:-8000}
    depends_on:
      - backend
    networks:
      - tahimoto-network

  backend:
    container_name: tahimoto-backend
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    volumes:
      - ./backend:/app
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    depends_on:
      - neo4j
      - redis
    environment:
      - DEBUG=${DEBUG:-True}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - NEO4J_URI=${NEO4J_URI:-bolt://neo4j:7687}
      - NEO4J_USER=${NEO4J_USER:-neo4j}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_DB=${REDIS_DB:-0}
      - USE_AGENT_MAPPING=${USE_AGENT_MAPPING:-False}
      - AGENT_SERVICE_URL=${AGENT_SERVICE_URL:-http://agent:8001/api}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-["http://localhost:3000","http://frontend:3000"]}
      - SECRET_KEY=${SECRET_KEY:-change_this_to_a_random_string_in_production}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-60}
    networks:
      - tahimoto-network

  neo4j:
    container_name: tahimoto-neo4j
    image: neo4j:5-community
    ports:
      - "${NEO4J_HTTP_PORT:-7474}:7474" # HTTP
      - "${NEO4J_BOLT_PORT:-7687}:7687" # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    environment:
      - NEO4J_AUTH=neo4j/password
      # Increase memory settings for better performance
      - NEO4J_dbms_memory_pagecache_size=${NEO4J_PAGECACHE_SIZE:-1G}
      - NEO4J_dbms.memory.heap.initial_size=${NEO4J_HEAP_INITIAL_SIZE:-1G}
      - NEO4J_dbms_memory_heap_max__size=${NEO4J_HEAP_MAX_SIZE:-2G}
    networks:
      - tahimoto-network

  redis:
    container_name: tahimoto-redis
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6380}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - tahimoto-network

  # Optional: Uncomment when agent service is ready
  # agent:
  #   container_name: tahimoto-agent
  #   build:
  #     context: ./agent
  #     dockerfile: Dockerfile.dev
  #   volumes:
  #     - ./agent:/app
  #   ports:
  #     - "${AGENT_PORT:-8001}:8001"
  #   depends_on:
  #     - neo4j
  #   environment:
  #     - DEBUG=${DEBUG:-True}
  #     - NEO4J_URI=${NEO4J_URI:-bolt://neo4j:7687}
  #     - NEO4J_USER=${NEO4J_USER:-neo4j}
  #     - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
  #     - OPENAI_API_KEY=${OPENAI_API_KEY}
  #   networks:
  #     - tahimoto-network

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  redis_data:

networks:
  tahimoto-network:
    driver: bridge