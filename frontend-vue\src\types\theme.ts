export enum ThemeCategory {
  // Core universal categories
  MOOD = 'MOOD',
  NARRATIVE_STRUCTURE = 'NARRATIVE_STRUCTURE',
  CHARACTER_DYNAMIC = 'CHARACTER_DYNAMIC',
  SETTING_TYPE = 'SETTING_TYPE',
  
  // Media-specific categories
  ANIME_SPECIFIC = 'ANIME_SPECIFIC',
  TV_SPECIFIC = 'TV_SPECIFIC',
  BOOK_SPECIFIC = 'BOOK_SPECIFIC',
  MUSIC_SPECIFIC = 'MUSIC_SPECIFIC',
  
  // Cross-media categories
  CULTURAL_CONTEXT = 'CULTURAL_CONTEXT',
  ARTISTIC_STYLE = 'ARTISTIC_STYLE',
  THEMATIC_DEPTH = 'THEMATIC_DEPTH',
  AUDIENCE_APPEAL = 'AUDIENCE_APPEAL',
}

export enum MediaType {
  ANIME = 'ANIME',
  TV = 'TV',
  BOOK = 'BOOK',
  MUSIC = 'MUSIC',
  MOVIE = 'MOVI<PERSON>',
  GAME = 'GAME',
}

export interface Theme {
  id: string
  name: string
  description: string
  category: ThemeCategory
  media_types: MediaType[]
  examples?: string[]
  cross_media_mappings?: Record<string, any>
  llm_context?: string
  confidence?: number
}

export interface ThemeSearchQuery {
  query?: string
  media_types?: MediaType[]
  categories?: ThemeCategory[]
  limit?: number
  offset?: number
}

export interface ThemeSearchResponse {
  themes: Theme[]
  total_count: number
  query_metadata: Record<string, any>
}

export interface MediaAnalysisRequest {
  title: string
  description?: string
  media_type: MediaType
  additional_context?: string
}

export interface MediaAnalysisResponse {
  detected_themes: Theme[]
  confidence_scores: Record<string, number>
  analysis_metadata: Record<string, any>
}

export interface MCPCapabilities {
  server_info: {
    name: string
    version: string
    description: string
  }
  capabilities: {
    resources: Array<{
      uri_template: string
      name: string
      description: string
    }>
    tools: Array<{
      name: string
      description: string
    }>
  }
  supported_media_types: string[]
  supported_categories: string[]
}

// Anime/Story related types
export interface AnimeSearchResult {
  id: string
  external_id: string
  title_english?: string
  title_romaji?: string
  title_native?: string
  synopsis?: string
  description?: string // Keep both for compatibility
  genres: string[]
  tags?: Array<{
    id: number
    name: string
    description?: string
    category?: string
    rank?: number
  }>
  media_type?: string
  format?: string
  status?: string
  episodes?: number
  duration?: number
  season?: string
  season_year?: number
  cover_image_medium?: string
  cover_image_large?: string
  cover_image?: {
    large?: string
    medium?: string
  }
  banner_image?: string
  popularity?: number
  average_score?: number
  source?: string
  start_date?: {
    year?: number
    month?: number
    day?: number
  }
  end_date?: {
    year?: number
    month?: number
    day?: number
  }
}

export interface AnimeSearchResponse {
  items: AnimeSearchResult[]
  total: number
  page: number
  per_page: number
  total_pages: number
}

export interface AnimeSearchQuery {
  query: string
  page?: number
  per_page?: number
  media_type?: string
  sort?: string
}

// Theme mapping types
export interface ThemeMapping {
  theme_id: string
  theme_name: string
  theme_category: ThemeCategory
  mapping_strength: number
  mapping_type: 'PRIMARY' | 'SECONDARY' | 'TERTIARY'
  source: string
  context: string
}

export interface MappingStats {
  total_themes_in_db: number
  mappable_genres: number
  mappable_tags: number
  total_mappings: number
  anime_with_mappings: number
  average_mappings_per_anime: number
}

export interface MapperInfo {
  description: string
  version: string
  total_themes_available: number
  genre_mappings: Record<string, string>
  tag_mappings: Record<string, string>
  features: string[]
}
