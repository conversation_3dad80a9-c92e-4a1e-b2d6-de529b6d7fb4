"""
Theme mapping API endpoints for connecting anime metadata to cross-media themes.
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import logging

from app.services.theme_mapping_service import ThemeMappingService
from app.services.mapping.cross_media_anime_mapper import CrossMediaAnimeMapper

logger = logging.getLogger(__name__)

router = APIRouter()

# Request/Response models
class AnimeMetadata(BaseModel):
    """Anime metadata for theme mapping."""
    title_romaji: str
    title_english: Optional[str] = None
    genres: List[str] = []
    tags: List[Dict[str, Any]] = []
    synopsis: Optional[str] = None

class ThemeMappingResponse(BaseModel):
    """Response model for theme mappings."""
    theme_id: str
    theme_name: str
    theme_category: str
    mapping_strength: float
    mapping_type: str
    source: str
    context: str

class MappingStatsResponse(BaseModel):
    """Response model for mapping statistics."""
    total_themes_in_db: int
    mappable_genres: int
    mappable_tags: int
    total_mappings: int
    anime_with_mappings: int
    average_mappings_per_anime: float

# Dependency to get services
async def get_theme_mapping_service() -> ThemeMappingService:
    """Get theme mapping service instance."""
    return ThemeMappingService()

async def get_anime_mapper() -> CrossMediaAnimeMapper:
    """Get anime mapper instance."""
    return CrossMediaAnimeMapper()

@router.post("/anime/map-themes", response_model=List[ThemeMappingResponse])
async def map_anime_themes(
    anime_metadata: AnimeMetadata,
    mapper: CrossMediaAnimeMapper = Depends(get_anime_mapper)
) -> List[ThemeMappingResponse]:
    """
    Map anime metadata to cross-media themes.
    
    This endpoint takes anime metadata (genres, tags, etc.) and returns
    a list of matching themes with confidence scores and mapping details.
    """
    try:
        # Convert to dict format expected by mapper
        metadata_dict = {
            "title_romaji": anime_metadata.title_romaji,
            "title_english": anime_metadata.title_english,
            "genres": anime_metadata.genres,
            "tags": anime_metadata.tags,
            "synopsis": anime_metadata.synopsis
        }
        
        # Get theme mappings
        mappings = await mapper.map_anime_to_themes(metadata_dict)
        
        # Convert to response format
        response_mappings = []
        for mapping in mappings:
            theme = mapping["theme"]
            response_mappings.append(ThemeMappingResponse(
                theme_id=theme["id"],
                theme_name=theme["name"],
                theme_category=theme["category"],
                mapping_strength=mapping["mapping_strength"],
                mapping_type=mapping["mapping_type"],
                source=mapping["source"],
                context=mapping["context"]
            ))
        
        logger.info(f"Mapped {len(response_mappings)} themes for anime: {anime_metadata.title_romaji}")
        return response_mappings
        
    except Exception as e:
        logger.error(f"Error mapping anime themes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error mapping themes: {str(e)}")

@router.get("/anime/{anime_id}/themes", response_model=List[Dict[str, Any]])
async def get_anime_themes(
    anime_id: str,
    service: ThemeMappingService = Depends(get_theme_mapping_service)
) -> List[Dict[str, Any]]:
    """
    Get existing theme mappings for a specific anime.
    
    Returns all theme mappings that have been created for the given anime ID.
    """
    try:
        mappings = await service.get_anime_theme_mappings(anime_id)
        return mappings
        
    except Exception as e:
        logger.error(f"Error getting anime themes for {anime_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting themes: {str(e)}")

@router.post("/anime/{anime_id}/create-mappings")
async def create_anime_theme_mappings(
    anime_id: str,
    anime_metadata: AnimeMetadata,
    service: ThemeMappingService = Depends(get_theme_mapping_service)
) -> Dict[str, Any]:
    """
    Create theme mappings for a specific anime in the database.
    
    This endpoint creates actual theme mapping relationships in the database
    for the given anime ID based on its metadata.
    """
    try:
        # Convert to dict format
        metadata_dict = {
            "title_romaji": anime_metadata.title_romaji,
            "title_english": anime_metadata.title_english,
            "genres": anime_metadata.genres,
            "tags": anime_metadata.tags,
            "synopsis": anime_metadata.synopsis
        }
        
        # Create mappings in database
        created_mappings = await service.create_anime_theme_mappings(anime_id, metadata_dict)
        
        return {
            "anime_id": anime_id,
            "created_mappings": len(created_mappings),
            "mappings": [
                {
                    "theme_id": mapping["theme"]["id"],
                    "theme_name": mapping["theme"]["name"],
                    "mapping_strength": mapping["relationship"]["mapping_strength"],
                    "mapping_type": mapping["relationship"]["mapping_type"]
                }
                for mapping in created_mappings
            ]
        }
        
    except Exception as e:
        logger.error(f"Error creating anime theme mappings for {anime_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating mappings: {str(e)}")

@router.get("/stats", response_model=MappingStatsResponse)
async def get_mapping_statistics(
    service: ThemeMappingService = Depends(get_theme_mapping_service)
) -> MappingStatsResponse:
    """
    Get statistics about the theme mapping system.
    
    Returns information about themes, mappings, and coverage.
    """
    try:
        stats = await service.get_mapping_statistics()
        
        return MappingStatsResponse(
            total_themes_in_db=stats["mapper_stats"]["total_themes_in_db"],
            mappable_genres=stats["mapper_stats"]["mappable_genres"],
            mappable_tags=stats["mapper_stats"]["mappable_tags"],
            total_mappings=stats["total_mappings"],
            anime_with_mappings=stats["anime_with_mappings"],
            average_mappings_per_anime=stats["average_mappings_per_anime"]
        )
        
    except Exception as e:
        logger.error(f"Error getting mapping statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting statistics: {str(e)}")

@router.get("/mapper/info")
async def get_mapper_info(
    mapper: CrossMediaAnimeMapper = Depends(get_anime_mapper)
) -> Dict[str, Any]:
    """
    Get information about the anime mapper configuration.
    
    Returns the current genre and tag mappings used by the mapper.
    """
    try:
        stats = await mapper.get_mapping_stats()
        return {
            "description": "Cross-media anime theme mapper for MVP",
            "version": "1.0.0",
            "total_themes_available": stats["total_themes_in_db"],
            "genre_mappings": stats["genre_mappings"],
            "tag_mappings": stats["tag_mappings"],
            "features": [
                "Direct genre-to-theme mapping",
                "AniList tag-to-theme mapping", 
                "Confidence scoring based on tag rank",
                "Heuristic enhancement for common patterns",
                "Cross-media theme support"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting mapper info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting mapper info: {str(e)}")

@router.delete("/anime/{anime_id}/themes")
async def refresh_anime_mappings(
    anime_id: str,
    anime_metadata: AnimeMetadata,
    service: ThemeMappingService = Depends(get_theme_mapping_service)
) -> Dict[str, Any]:
    """
    Refresh theme mappings for an anime by deleting old ones and creating new ones.
    
    Useful when anime metadata has been updated and mappings need to be recalculated.
    """
    try:
        # Convert to dict format
        metadata_dict = {
            "title_romaji": anime_metadata.title_romaji,
            "title_english": anime_metadata.title_english,
            "genres": anime_metadata.genres,
            "tags": anime_metadata.tags,
            "synopsis": anime_metadata.synopsis
        }
        
        # Refresh mappings
        new_mappings = await service.refresh_anime_mappings(anime_id, metadata_dict)
        
        return {
            "anime_id": anime_id,
            "action": "refreshed",
            "new_mappings_count": len(new_mappings),
            "message": f"Successfully refreshed theme mappings for anime {anime_id}"
        }
        
    except Exception as e:
        logger.error(f"Error refreshing anime theme mappings for {anime_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error refreshing mappings: {str(e)}")
