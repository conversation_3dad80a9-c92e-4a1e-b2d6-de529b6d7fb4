# Technology Stack

## Frontend

-   **Framework:** Vue 3 + TypeScript
    -   Component-based architecture with Composition API
    -   Type-safe development
    -   Modern Vue patterns (composables, reactive state)
-   **Build Tools:** Vite
    -   Fast development server with hot module replacement
    -   Efficient production builds
-   **State Management:** Pinia
    -   Modern state management for Vue applications
-   **Routing:** Vue Router
    -   Official routing library for Vue.js
-   **UI Components:**
    -   Custom components for story display
    -   Responsive design patterns

## Backend (API)

-   **Framework:** FastAPI (Python)
    -   Async request handling
    -   Pydantic data validation
    -   OpenAPI/Swagger support
-   **GraphQL Server:** Ariadne
    -   Schema-first or Python-first schema definition
    -   Easy custom scalar implementation (for SourceId)
-   **Database ORM:**  *None* (Direct Neo4j driver usage)
-   **Thematic Mapping:**
    -   Abstract Base Classes (ABC) for interface definitions
    -   Factory pattern for implementation selection
    -   Support for both manual and agent-based mapping
    -   Media-specific implementations (anime, books, movies)

## Agent Service

-    **Framework:** FastAPI
    -   Async support for potential LLM API
-   **LLM Integration:** (Details to be determined based on specific LLM choice)
    -   Persistent Memory: SSM (State Space Models) / Transformers.
    -   Tool Use: Custom functions for interacting with external APIs.
    -  API calls to LLM.
- **Language Model**: (Details to be updated when chosen)
-   **Thematic Analysis:**
    -   Advanced theme extraction from textual descriptions
    -   Confidence scoring for theme mapping
    -   Integration with the Backend's ThemeMapper interface

## Database

-   **Neo4j:** Graph database
    -   Stores stories, themes, and relationships as a graph.
    -   Cypher query language.
    -   Optimized for traversing thematic connections.
    -   Supports both manual and agent-identified themes.

## Caching

-   **Redis:**
    -   API response caching.
    -   Frequent data caching
    -   Caching of theme mappings for performance

## Infrastructure

-   **Containerization:** Docker
    -   Multi-container setup (Frontend, Backend, Agent, Neo4j, Redis).
    -   Docker Compose for local development.
-   **Development Environment:**
    -   Hot reloading for frontend.
    -   Auto-restart for backend services.

## Planned Enhancements

-   **API Documentation:** Complete OpenAPI/Swagger documentation.
-   **Monitoring:** Application metrics, error tracking, performance monitoring.
-   **Security:** Rate limiting, input validation, secure headers.
-  **Admin Dashboard:**  Vue/TypeScript frontend for managing theme mappings.

## Future Considerations

-   **Scalability:** Load balancing, database replication (Neo4j clustering).
-   **Edge Computing:** Lightweight agent for pre-processing on the edge.
-   **Hybrid Mapping:** Combined approach using both manual rules and agent insights for theme mapping.