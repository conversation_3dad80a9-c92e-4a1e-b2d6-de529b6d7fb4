#!/usr/bin/env python
"""
Initialize comprehensive cross-media core themes for <PERSON>.
This script populates the database with essential themes that work across
anime, TV, books, and music, with rich metadata for LLM consumption.
"""
import asyncio
import json
import os
import sys
import uuid
from datetime import datetime
from pathlib import Path

# Add the parent directory to the sys.path for imports to work
sys.path.append(str(Path(__file__).parent.parent))

from app.db.neo4j_session import driver
from app.schemas.theme_category import ThemeCategory, MediaType
from app.schemas.theme import ThemeStatus

# Define comprehensive core themes organized by category with cross-media support
CORE_THEMES = {
    ThemeCategory.MOOD: [
        {
            "name": "Cozy",
            "description": "Warm, comfortable, safe feelings that create a sense of security and contentment.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Creates intimate, homey atmosphere. In anime: slice-of-life settings. In TV: family sitcoms. In books: domestic fiction. In music: acoustic, folk genres.",
            "usage_examples": ["Anime: Non Non Biyori", "TV: The Great British Bake Off", "Book: Anne of Green Gables", "Music: Lo-fi hip hop"],
            "cross_media_mappings": {"ANIME": "Iyashikei", "TV": "Comfort viewing", "BOOK": "Cozy mystery", "MUSIC": "Ambient/chill"}
        },
        {
            "name": "Tense",
            "description": "Anxiety-inducing, on-edge atmosphere that creates suspense and unease.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Builds suspense through pacing, music, and visual/narrative techniques. Universal across all media for thriller genres.",
            "usage_examples": ["Anime: Attack on Titan", "TV: Breaking Bad", "Book: Gone Girl", "Music: Horror movie soundtracks"],
            "cross_media_mappings": {"ANIME": "Psychological thriller", "TV": "Suspense drama", "BOOK": "Thriller novel", "MUSIC": "Dissonant/atonal"}
        },
        {
            "name": "Melancholic",
            "description": "Wistful, reflective, tinged with sadness and contemplative emotions.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Evokes bittersweet emotions, nostalgia, and introspection. Often paired with themes of loss, memory, or change.",
            "usage_examples": ["Anime: Your Name", "TV: This Is Us", "Book: The Remains of the Day", "Music: Radiohead"],
            "cross_media_mappings": {"ANIME": "Mono no aware", "TV": "Drama series", "BOOK": "Literary fiction", "MUSIC": "Indie/alternative"}
        },
        {
            "name": "Uplifting",
            "description": "Inspiring, positive emotional response that elevates the spirit.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Motivational and encouraging content that promotes hope, growth, and positive change.",
            "usage_examples": ["Anime: Haikyuu!!", "TV: Ted Lasso", "Book: The Alchemist", "Music: Gospel/inspirational"],
            "cross_media_mappings": {"ANIME": "Sports/shounen", "TV": "Feel-good comedy", "BOOK": "Self-help/inspiration", "MUSIC": "Upbeat pop"}
        },
        {
            "name": "Dark",
            "description": "Serious, heavy atmosphere dealing with difficult or disturbing themes.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Explores mature themes like mortality, corruption, psychological trauma. Often features moral ambiguity.",
            "usage_examples": ["Anime: Berserk", "TV: The Wire", "Book: 1984", "Music: Black metal"],
            "cross_media_mappings": {"ANIME": "Seinen/mature", "TV": "Prestige drama", "BOOK": "Dystopian fiction", "MUSIC": "Dark ambient"}
        },
        {
            "name": "Comedic",
            "description": "Humorous, light-hearted tone designed to amuse and entertain.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Uses various comedy techniques: slapstick, wordplay, situational humor, character-based comedy.",
            "usage_examples": ["Anime: Gintama", "TV: Brooklyn Nine-Nine", "Book: Good Omens", "Music: Weird Al Yankovic"],
            "cross_media_mappings": {"ANIME": "Gag comedy", "TV": "Sitcom", "BOOK": "Humorous fiction", "MUSIC": "Comedy/parody"}
        },
        {
            "name": "Romantic",
            "description": "Love-focused, heartwarming emotional tone centered on relationships.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Explores romantic relationships, emotional intimacy, and the journey of love in various forms.",
            "usage_examples": ["Anime: Your Lie in April", "TV: Pride and Prejudice", "Book: Pride and Prejudice", "Music: Love ballads"],
            "cross_media_mappings": {"ANIME": "Romance/shoujo", "TV": "Romantic drama", "BOOK": "Romance novel", "MUSIC": "Love songs"}
        },
        {
            "name": "Nostalgic",
            "description": "Evokes memories of the past with sentimental longing and wistful affection.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Triggers emotional connection to past experiences, childhood, or bygone eras through familiar elements.",
            "usage_examples": ["Anime: Only Yesterday", "TV: Stranger Things", "Book: To Kill a Mockingbird", "Music: Synthwave"],
            "cross_media_mappings": {"ANIME": "Coming-of-age", "TV": "Period drama", "BOOK": "Memoir/autobiography", "MUSIC": "Retro/vintage"}
        }
    ],
    ThemeCategory.NARRATIVE_STRUCTURE: [
        {
            "name": "Journey",
            "description": "Physical and/or metaphorical travel where characters progress through changing environments.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Classic narrative structure involving movement, discovery, and transformation. Can be literal travel or personal growth journey.",
            "usage_examples": ["Anime: Kino's Journey", "TV: The Lord of the Rings", "Book: The Odyssey", "Music: Concept albums"],
            "cross_media_mappings": {"ANIME": "Adventure series", "TV": "Road trip/quest", "BOOK": "Picaresque novel", "MUSIC": "Progressive rock"}
        },
        {
            "name": "Coming of Age",
            "description": "Evolution from youth to maturity focusing on character growth and self-discovery.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Universal theme of transition from childhood to adulthood, often involving first experiences and moral development.",
            "usage_examples": ["Anime: A Silent Voice", "TV: Freaks and Geeks", "Book: The Catcher in the Rye", "Music: Green Day's American Idiot"],
            "cross_media_mappings": {"ANIME": "School life/youth", "TV": "Teen drama", "BOOK": "Bildungsroman", "MUSIC": "Pop punk/emo"}
        },
        {
            "name": "Mystery",
            "description": "Incremental revelation of hidden information driving the narrative forward.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Puzzle-solving narrative that engages audience in deduction and revelation. Creates suspense through information control.",
            "usage_examples": ["Anime: Detective Conan", "TV: Sherlock", "Book: Agatha Christie novels", "Music: Murder ballads"],
            "cross_media_mappings": {"ANIME": "Detective/mystery", "TV": "Procedural drama", "BOOK": "Whodunit", "MUSIC": "Narrative folk"}
        },
        {
            "name": "Slice of Life",
            "description": "Realistic portrayal of everyday experiences without dramatic plot devices.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Focuses on mundane, relatable experiences to find meaning in ordinary moments. Emphasizes character over plot.",
            "usage_examples": ["Anime: Barakamon", "TV: The Office", "Book: Normal People", "Music: Indie folk"],
            "cross_media_mappings": {"ANIME": "Iyashikei/nichijou", "TV": "Workplace comedy", "BOOK": "Literary realism", "MUSIC": "Singer-songwriter"}
        },
        {
            "name": "Tournament",
            "description": "Structured competition with escalating challenges and defined rules.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Competition-based narrative with clear progression, stakes, and character development through challenges.",
            "usage_examples": ["Anime: Dragon Ball Z", "TV: The Voice", "Book: The Hunger Games", "Music: Battle rap"],
            "cross_media_mappings": {"ANIME": "Battle shounen", "TV": "Competition reality", "BOOK": "Dystopian games", "MUSIC": "Competitive genres"}
        },
        {
            "name": "Ensemble",
            "description": "Multiple character arcs of similar importance interweaving throughout the story.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Multi-protagonist narrative where several characters share focus and development, creating complex relationship dynamics.",
            "usage_examples": ["Anime: Durarara!!", "TV: Game of Thrones", "Book: A Song of Ice and Fire", "Music: Supergroups"],
            "cross_media_mappings": {"ANIME": "Multi-POV series", "TV": "Ensemble drama", "BOOK": "Multi-POV novel", "MUSIC": "Collaborative albums"}
        },
        {
            "name": "Tragedy",
            "description": "Inevitable downfall of characters despite their efforts to prevent it.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Classical dramatic structure involving fatal flaws, hubris, and cathartic emotional release through suffering.",
            "usage_examples": ["Anime: Akame ga Kill!", "TV: Breaking Bad", "Book: Macbeth", "Music: Opera"],
            "cross_media_mappings": {"ANIME": "Dark/mature series", "TV": "Prestige drama", "BOOK": "Classical tragedy", "MUSIC": "Tragic opera/ballads"}
        }
    ],
    ThemeCategory.CHARACTER_DYNAMIC: [
        {
            "name": "Found Family",
            "description": "Non-blood relations forming familial bonds and support structures.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Chosen relationships that provide family-like support, often among outcasts or those separated from biological family.",
            "usage_examples": ["Anime: Fairy Tail", "TV: Friends", "Book: The Outsiders", "Music: Band dynamics"],
            "cross_media_mappings": {"ANIME": "Nakama bonds", "TV": "Chosen family", "BOOK": "Found family trope", "MUSIC": "Musical collectives"}
        },
        {
            "name": "Rivalry",
            "description": "Competitive relationship driving mutual growth and development.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Competitive dynamic that pushes characters to improve, often evolving into respect or friendship.",
            "usage_examples": ["Anime: Naruto vs Sasuke", "TV: Sherlock vs Moriarty", "Book: Harry vs Draco", "Music: Rap battles"],
            "cross_media_mappings": {"ANIME": "Rival characters", "TV": "Antagonistic relationship", "BOOK": "Foil characters", "MUSIC": "Musical competition"}
        },
        {
            "name": "Mentor-Student",
            "description": "Knowledge and wisdom transfer relationship with clear hierarchy.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Teaching relationship involving guidance, growth, and eventual independence of the student.",
            "usage_examples": ["Anime: All Might & Deku", "TV: Mr. Miyagi & Daniel", "Book: Dumbledore & Harry", "Music: Master-apprentice"],
            "cross_media_mappings": {"ANIME": "Sensei-student", "TV": "Teacher-student", "BOOK": "Wise mentor", "MUSIC": "Musical lineage"}
        },
        {
            "name": "Romance",
            "description": "Romantic relationship development with emotional and/or physical attraction.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Exploration of romantic love in various forms: first love, mature relationships, unrequited love, etc.",
            "usage_examples": ["Anime: Toradora!", "TV: The Office (Jim & Pam)", "Book: Jane Austen novels", "Music: Love songs"],
            "cross_media_mappings": {"ANIME": "Romance subplot", "TV": "Will-they-won't-they", "BOOK": "Romance arc", "MUSIC": "Love ballads"}
        },
        {
            "name": "Friendship",
            "description": "Deep platonic bonds based on mutual respect, trust, and shared experiences.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Non-romantic relationships that provide emotional support, loyalty, and personal growth through companionship.",
            "usage_examples": ["Anime: One Piece crew", "TV: How I Met Your Mother", "Book: Lord of the Rings fellowship", "Music: Collaborative artists"],
            "cross_media_mappings": {"ANIME": "Friendship power", "TV": "Friend group", "BOOK": "Loyal companions", "MUSIC": "Musical partnerships"}
        },
        {
            "name": "Lone Wolf",
            "description": "Character operating independently despite social contexts and pressures.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Independent character who prefers solitude but may learn the value of connection through story progression.",
            "usage_examples": ["Anime: Kenshin Himura", "TV: House MD", "Book: The Stranger", "Music: Solo artists"],
            "cross_media_mappings": {"ANIME": "Stoic protagonist", "TV": "Anti-social genius", "BOOK": "Isolated protagonist", "MUSIC": "Solo performer"}
        },
        {
            "name": "Team Dynamics",
            "description": "Group working together toward common goals with defined roles and relationships.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Collaborative relationships where individual strengths combine to achieve shared objectives.",
            "usage_examples": ["Anime: Haikyuu!!", "TV: The A-Team", "Book: Ocean's Eleven", "Music: Bands/orchestras"],
            "cross_media_mappings": {"ANIME": "Sports team", "TV": "Heist crew", "BOOK": "Adventure party", "MUSIC": "Musical ensemble"}
        }
    ],
    ThemeCategory.SETTING_TYPE: [
        {
            "name": "School Life",
            "description": "Educational institution as primary setting with academic activities.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "School environment providing structure for coming-of-age stories, social dynamics, and academic challenges.",
            "usage_examples": ["Anime: K-On!", "TV: Glee", "Book: Harry Potter", "Music: School-themed songs"],
            "cross_media_mappings": {"ANIME": "School setting", "TV": "Teen drama", "BOOK": "School story", "MUSIC": "Youth culture"}
        },
        {
            "name": "Fantasy World",
            "description": "Imaginary setting with its own rules, often including magical elements.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Created worlds with unique magic systems, creatures, and societies that allow exploration of themes impossible in reality.",
            "usage_examples": ["Anime: Made in Abyss", "TV: Game of Thrones", "Book: Lord of the Rings", "Music: Fantasy metal"],
            "cross_media_mappings": {"ANIME": "Isekai/fantasy", "TV": "Fantasy epic", "BOOK": "High fantasy", "MUSIC": "Symphonic metal"}
        },
        {
            "name": "Urban Modern",
            "description": "Contemporary city setting with modern technology and social structures.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Present-day urban environment reflecting current social issues, technology, and lifestyle challenges.",
            "usage_examples": ["Anime: Psycho-Pass", "TV: Friends", "Book: The Devil Wears Prada", "Music: Hip-hop/urban"],
            "cross_media_mappings": {"ANIME": "Modern city", "TV": "Urban sitcom", "BOOK": "Contemporary fiction", "MUSIC": "Urban genres"}
        },
        {
            "name": "Historical",
            "description": "Based in real historical period with period-appropriate details.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Past time periods that provide context for exploring how people lived, thought, and faced challenges in different eras.",
            "usage_examples": ["Anime: Vinland Saga", "TV: The Crown", "Book: War and Peace", "Music: Period music"],
            "cross_media_mappings": {"ANIME": "Historical drama", "TV": "Period piece", "BOOK": "Historical fiction", "MUSIC": "Traditional/folk"}
        },
        {
            "name": "Rural",
            "description": "Countryside or small town setting with nature prominence.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Natural settings emphasizing simplicity, community bonds, and connection to nature and traditional ways of life.",
            "usage_examples": ["Anime: Silver Spoon", "TV: Gilmore Girls", "Book: To Kill a Mockingbird", "Music: Country/folk"],
            "cross_media_mappings": {"ANIME": "Countryside life", "TV": "Small town drama", "BOOK": "Rural fiction", "MUSIC": "Country/folk"}
        },
        {
            "name": "Sci-Fi Future",
            "description": "Futuristic setting with advanced technology and speculative elements.",
            "media_types": ["ANIME", "TV", "BOOK", "MUSIC"],
            "llm_context": "Future worlds exploring technological advancement's impact on humanity, society, and individual identity.",
            "usage_examples": ["Anime: Ghost in the Shell", "TV: Black Mirror", "Book: Neuromancer", "Music: Synthwave/electronic"],
            "cross_media_mappings": {"ANIME": "Cyberpunk/mecha", "TV": "Sci-fi series", "BOOK": "Science fiction", "MUSIC": "Electronic/synth"}
        }
    ]
}

async def create_theme_direct(session, theme_data, category):
    """Create a theme directly using a Cypher query with cross-media support."""
    theme_id = f"theme_{uuid.uuid4()}"
    now = datetime.now().isoformat()
    
    query = """
    CREATE (t:Theme {
        id: $id,
        name: $name,
        description: $description,
        category: $category,
        status: $status,
        confidence: $confidence,
        created_at: $created_at,
        updated_at: $updated_at,
        media_types: $media_types,
        llm_context: $llm_context,
        usage_examples: $usage_examples,
        cross_media_mappings: $cross_media_mappings
    })
    RETURN t
    """
    
    params = {
        "id": theme_id,
        "name": theme_data["name"],
        "description": theme_data["description"],
        "category": category,
        "status": "VERIFIED",
        "confidence": 1.0,
        "created_at": now,
        "updated_at": now,
        "media_types": theme_data.get("media_types", []),
        "llm_context": theme_data.get("llm_context", ""),
        "usage_examples": theme_data.get("usage_examples", []),
        "cross_media_mappings": json.dumps(theme_data.get("cross_media_mappings", {}))
    }
    
    try:
        result = await session.run(query, params)
        record = await result.single()
        if record:
            print(f"Created theme: {theme_data['name']} ({theme_id}) - Media: {', '.join(theme_data.get('media_types', []))}")
            return theme_id
        else:
            print(f"Error creating theme: {theme_data['name']}")
            return None
    except Exception as e:
        print(f"Error creating theme {theme_data['name']}: {str(e)}")
        return None

async def create_themes():
    """Create core themes."""
    async with driver.session() as session:
        # Clear existing themes first
        print("Clearing existing themes...")
        await session.run("MATCH (t:Theme) DETACH DELETE t")
        
        # Create themes for each category
        print("Creating MOOD themes...")
        for theme_data in CORE_THEMES[ThemeCategory.MOOD]:
            await create_theme_direct(session, theme_data, "MOOD")
        
        print("Creating NARRATIVE_STRUCTURE themes...")
        for theme_data in CORE_THEMES[ThemeCategory.NARRATIVE_STRUCTURE]:
            await create_theme_direct(session, theme_data, "NARRATIVE_STRUCTURE")

        print("Creating CHARACTER_DYNAMIC themes...")
        for theme_data in CORE_THEMES[ThemeCategory.CHARACTER_DYNAMIC]:
            await create_theme_direct(session, theme_data, "CHARACTER_DYNAMIC")

        print("Creating SETTING_TYPE themes...")
        for theme_data in CORE_THEMES[ThemeCategory.SETTING_TYPE]:
            await create_theme_direct(session, theme_data, "SETTING_TYPE")

        print("\nCore cross-media themes initialization complete!")
        print(f"Total themes created: {len(CORE_THEMES[ThemeCategory.MOOD]) + len(CORE_THEMES[ThemeCategory.NARRATIVE_STRUCTURE]) + len(CORE_THEMES[ThemeCategory.CHARACTER_DYNAMIC]) + len(CORE_THEMES[ThemeCategory.SETTING_TYPE])}")

if __name__ == "__main__":
    asyncio.run(create_themes())
