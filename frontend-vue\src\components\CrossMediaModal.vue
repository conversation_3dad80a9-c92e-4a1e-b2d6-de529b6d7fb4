<template>
  <div
    v-if="show"
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
  >
    <!-- Background overlay -->
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        aria-hidden="true"
        @click="$emit('close')"
      ></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
        <!-- Header -->
        <div class="bg-white px-6 py-4 border-b border-gray-200">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-2xl font-bold text-gray-900" id="modal-title">
                Cross-Media Connections
              </h3>
              <p class="text-lg text-gray-600 mt-1">
                {{ theme.name }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Explore how this theme appears across different media types
              </p>
            </div>
            <button
              @click="$emit('close')"
              class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="bg-white px-6 py-6 max-h-96 overflow-y-auto">
          <!-- No mappings state -->
          <div v-if="!hasCrossMediaMappings" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Cross-Media Mappings</h3>
            <p class="text-gray-500">
              This theme doesn't have specific cross-media mappings defined yet.
            </p>
          </div>

          <!-- Cross-Media Mappings Grid -->
          <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div
              v-for="(mapping, mediaType) in theme.cross_media_mappings"
              :key="mediaType"
              class="border border-gray-200 rounded-lg overflow-hidden"
            >
              <!-- Media Type Header -->
              <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <div class="flex items-center">
                  <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getMediaTypeIcon(mediaType)" />
                    </svg>
                    <h4 class="text-lg font-semibold text-gray-900">
                      {{ mediaType.toUpperCase() }}
                    </h4>
                  </div>
                  <span class="ml-auto inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium"
                        :class="getMediaTypeBadgeClass(mediaType as any)">
                    {{ getMediaTypeLabel(mediaType) }}
                  </span>
                </div>
              </div>

              <!-- Mapping Content -->
              <div class="p-4 space-y-4">
                <!-- Genres -->
                <div v-if="mapping.genres && mapping.genres.length > 0">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Genres</h5>
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="genre in mapping.genres"
                      :key="genre"
                      class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
                    >
                      {{ genre }}
                    </span>
                  </div>
                </div>

                <!-- Tags -->
                <div v-if="mapping.tags && mapping.tags.length > 0">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Tags</h5>
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="tag in mapping.tags"
                      :key="tag"
                      class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-md"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </div>

                <!-- Examples -->
                <div v-if="mapping.examples && mapping.examples.length > 0">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Examples</h5>
                  <div class="space-y-1">
                    <div
                      v-for="example in mapping.examples"
                      :key="example"
                      class="text-sm text-gray-600 bg-gray-50 rounded px-2 py-1"
                    >
                      {{ example }}
                    </div>
                  </div>
                </div>

                <!-- Description -->
                <div v-if="mapping.description">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Description</h5>
                  <p class="text-sm text-gray-600">{{ mapping.description }}</p>
                </div>

                <!-- Context -->
                <div v-if="mapping.context">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Context</h5>
                  <p class="text-sm text-gray-600">{{ mapping.context }}</p>
                </div>

                <!-- Empty state for media type -->
                <div v-if="isEmptyMapping(mapping)" class="text-center py-4">
                  <p class="text-sm text-gray-500">No specific mappings defined for {{ mediaType }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Theme Context -->
          <div v-if="theme.llm_context" class="mt-8 border-t border-gray-200 pt-6">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Theme Context</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <p class="text-sm text-gray-700 leading-relaxed">
                {{ theme.llm_context }}
              </p>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex items-center justify-between">
          <div class="text-sm text-gray-500">
            Cross-media mappings help find similar themes across different media types
          </div>
          <button
            @click="$emit('close')"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Theme, MediaType } from '@/types/theme'

// Props
interface Props {
  theme: Theme
  show: boolean
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  close: []
}>()

// Computed
const hasCrossMediaMappings = computed(() => {
  return props.theme.cross_media_mappings && 
         Object.keys(props.theme.cross_media_mappings).length > 0
})

// Methods
function getMediaTypeBadgeClass(mediaType: MediaType): string {
  const classes = {
    ANIME: 'bg-pink-100 text-pink-800',
    TV: 'bg-blue-100 text-blue-800',
    BOOK: 'bg-yellow-100 text-yellow-800',
    MUSIC: 'bg-red-100 text-red-800',
    MOVIE: 'bg-purple-100 text-purple-800',
    GAME: 'bg-green-100 text-green-800',
  }
  return classes[mediaType] || 'bg-gray-100 text-gray-800'
}

function getMediaTypeIcon(mediaType: string): string {
  const icons = {
    anime: 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h3a1 1 0 011 1v14a1 1 0 01-1 1H4a1 1 0 01-1-1V5a1 1 0 011-1h3z',
    tv: 'M21 3H3v18h18V3zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z',
    book: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
    music: 'M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3',
    movie: 'M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h3a1 1 0 011 1v14a1 1 0 01-1 1H4a1 1 0 01-1-1V5a1 1 0 011-1h3z',
    game: 'M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 000 1.788l4 2.016V9.236l-4-2.016V11z'
  }
  return icons[mediaType.toLowerCase()] || icons.anime
}

function getMediaTypeLabel(mediaType: string): string {
  const labels = {
    anime: 'Animation',
    tv: 'Television',
    book: 'Literature',
    music: 'Audio',
    movie: 'Film',
    game: 'Interactive'
  }
  return labels[mediaType.toLowerCase()] || mediaType
}

function isEmptyMapping(mapping: any): boolean {
  if (!mapping) return true
  
  const hasGenres = mapping.genres && mapping.genres.length > 0
  const hasTags = mapping.tags && mapping.tags.length > 0
  const hasExamples = mapping.examples && mapping.examples.length > 0
  const hasDescription = mapping.description && mapping.description.trim()
  const hasContext = mapping.context && mapping.context.trim()
  
  return !hasGenres && !hasTags && !hasExamples && !hasDescription && !hasContext
}
</script>
