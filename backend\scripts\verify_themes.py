#!/usr/bin/env python
"""
Verify the cross-media themes were created successfully.
"""
import asyncio
import json
import sys
from pathlib import Path

# Add the parent directory to the sys.path for imports to work
sys.path.append(str(Path(__file__).parent.parent))

from app.db.neo4j_session import driver

async def verify_themes():
    """Verify themes were created with proper cross-media data."""
    async with driver.session() as session:
        # Count total themes
        result = await session.run('MATCH (t:Theme) RETURN count(t) as total')
        record = await result.single()
        total_themes = record["total"]
        print(f'Total themes in database: {total_themes}')
        
        # Count by category
        result = await session.run('''
            MATCH (t:Theme) 
            RETURN t.category as category, count(t) as count 
            ORDER BY category
        ''')
        print("\nThemes by category:")
        async for record in result:
            print(f"  {record['category']}: {record['count']}")
        
        # Get sample themes from each category
        result = await session.run('''
            MATCH (t:Theme) 
            WHERE t.category = "MOOD"
            RETURN t.name as name, t.media_types as media_types, t.cross_media_mappings as mappings
            LIMIT 2
        ''')
        print("\nSample MOOD themes:")
        async for record in result:
            mappings = json.loads(record['mappings']) if record['mappings'] else {}
            print(f"  {record['name']}: {record['media_types']} -> {mappings}")
        
        # Check for themes with all required fields
        result = await session.run('''
            MATCH (t:Theme) 
            WHERE t.llm_context IS NOT NULL AND t.usage_examples IS NOT NULL
            RETURN count(t) as complete_themes
        ''')
        record = await result.single()
        complete_themes = record["complete_themes"]
        print(f"\nThemes with complete cross-media data: {complete_themes}/{total_themes}")
        
        if complete_themes == total_themes:
            print("✅ All themes have complete cross-media metadata!")
        else:
            print("⚠️  Some themes are missing cross-media metadata")

if __name__ == "__main__":
    asyncio.run(verify_themes())
