<template>
  <div class="anime-theme-display">
    <!-- Theme Analysis Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Detected Themes</h3>
        <div class="flex items-center space-x-2">
          <button
            v-if="!hasThemes && !loading"
            @click="analyzeThemes"
            :disabled="analyzing"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <svg v-if="analyzing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            {{ analyzing ? 'Analyzing...' : 'Analyze Themes' }}
          </button>
          <button
            v-if="hasThemes"
            @click="refreshThemes"
            :disabled="analyzing"
            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <svg v-if="analyzing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading && !hasThemes" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-600">Loading existing themes...</span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error Loading Themes</h3>
            <div class="mt-2 text-sm text-red-700">
              <p>{{ error }}</p>
            </div>
            <div class="mt-4">
              <button @click="loadExistingThemes" class="text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200">
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- No Themes State -->
      <div v-else-if="!hasThemes && !loading" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Themes Detected</h3>
        <p class="text-gray-500 mb-4">
          Click "Analyze Themes" to automatically detect themes based on this anime's genres, tags, and synopsis.
        </p>
      </div>

      <!-- Themes Display -->
      <div v-else-if="hasThemes" class="space-y-4">
        <!-- Theme Stats -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-primary-600">{{ themes.length }}</div>
              <div class="text-sm text-gray-600">Total Themes</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-green-600">{{ primaryThemes.length }}</div>
              <div class="text-sm text-gray-600">Primary</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-yellow-600">{{ secondaryThemes.length }}</div>
              <div class="text-sm text-gray-600">Secondary</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-gray-600">{{ averageConfidence.toFixed(1) }}%</div>
              <div class="text-sm text-gray-600">Avg Confidence</div>
            </div>
          </div>
        </div>

        <!-- Theme Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="themeMapping in themes"
            :key="themeMapping.theme_id"
            class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
            @click="selectTheme(themeMapping)"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <h4 class="font-semibold text-gray-900">{{ themeMapping.theme_name }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ themeMapping.theme_category }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
                      :class="getMappingTypeBadgeClass(themeMapping.mapping_type)">
                  {{ themeMapping.mapping_type }}
                </span>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">
                    {{ Math.round(themeMapping.mapping_strength * 100) }}%
                  </div>
                  <div class="text-xs text-gray-500">confidence</div>
                </div>
              </div>
            </div>
            
            <p class="text-sm text-gray-700 mb-3">{{ themeMapping.context }}</p>
            
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>{{ themeMapping.source_type }}</span>
              <button
                @click.stop="viewThemeDetails(themeMapping)"
                class="text-primary-600 hover:text-primary-800 font-medium"
              >
                View Details →
              </button>
            </div>
          </div>
        </div>

        <!-- Cross-Media Connections -->
        <div v-if="hasThemes" class="mt-8 border-t border-gray-200 pt-6">
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-medium text-gray-900">Cross-Media Connections</h4>
            <router-link
              to="/themes"
              class="text-sm text-primary-600 hover:text-primary-800 font-medium"
            >
              Explore All Themes →
            </router-link>
          </div>
          <p class="text-sm text-gray-600 mb-4">
            These themes also appear in other media types, helping you discover similar stories across anime, TV, books, and music.
          </p>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="category in uniqueCategories"
              :key="category"
              class="text-center p-3 bg-gray-50 rounded-lg"
            >
              <div class="text-lg font-semibold text-gray-900">
                {{ getThemeCountByCategory(category) }}
              </div>
              <div class="text-sm text-gray-600">{{ formatCategory(category) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Theme Detail Modal -->
    <ThemeDetailModal
      v-if="selectedTheme"
      :theme="selectedTheme"
      :show="showThemeModal"
      @close="closeThemeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { themeMappingApi } from '@/services/api'
import type { AnimeSearchResult, ThemeMapping, Theme } from '@/types/theme'
import ThemeDetailModal from './ThemeDetailModal.vue'

// Props
interface Props {
  anime: AnimeSearchResult
  animeId: string
}

const props = defineProps<Props>()

// State
const themes = ref<ThemeMapping[]>([])
const selectedTheme = ref<Theme | null>(null)
const showThemeModal = ref(false)
const loading = ref(false)
const analyzing = ref(false)
const error = ref<string | null>(null)

// Computed
const hasThemes = computed(() => themes.value.length > 0)

const primaryThemes = computed(() => 
  themes.value.filter(t => t.mapping_type === 'PRIMARY')
)

const secondaryThemes = computed(() => 
  themes.value.filter(t => t.mapping_type === 'SECONDARY')
)

const averageConfidence = computed(() => {
  if (themes.value.length === 0) return 0
  const total = themes.value.reduce((sum, t) => sum + t.mapping_strength, 0)
  return (total / themes.value.length) * 100
})

const uniqueCategories = computed(() => {
  const categories = themes.value.map(t => t.theme_category)
  return [...new Set(categories)]
})

// Methods
async function loadExistingThemes() {
  try {
    loading.value = true
    error.value = null
    themes.value = await themeMappingApi.getAnimeThemes(props.animeId)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load themes'
    console.error('Error loading themes:', err)
  } finally {
    loading.value = false
  }
}

async function analyzeThemes() {
  try {
    analyzing.value = true
    error.value = null
    
    // Prepare anime metadata for analysis
    const animeMetadata = {
      title_romaji: props.anime.title_romaji || props.anime.title_english || 'Unknown',
      title_english: props.anime.title_english,
      genres: props.anime.genres || [],
      tags: props.anime.tags || [],
      synopsis: props.anime.synopsis || props.anime.description || ''
    }
    
    // Create theme mappings
    const mappings = await themeMappingApi.createAnimeThemeMappings(props.animeId, animeMetadata)
    themes.value = mappings
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to analyze themes'
    console.error('Error analyzing themes:', err)
  } finally {
    analyzing.value = false
  }
}

async function refreshThemes() {
  try {
    analyzing.value = true
    error.value = null
    
    const animeMetadata = {
      title_romaji: props.anime.title_romaji || props.anime.title_english || 'Unknown',
      title_english: props.anime.title_english,
      genres: props.anime.genres || [],
      tags: props.anime.tags || [],
      synopsis: props.anime.synopsis || props.anime.description || ''
    }
    
    // Refresh mappings (this will delete and recreate)
    const mappings = await themeMappingApi.refreshAnimeThemeMappings(props.animeId, animeMetadata)
    themes.value = mappings
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to refresh themes'
    console.error('Error refreshing themes:', err)
  } finally {
    analyzing.value = false
  }
}

function selectTheme(themeMapping: ThemeMapping) {
  // Convert ThemeMapping to Theme for the modal
  selectedTheme.value = {
    id: themeMapping.theme_id,
    name: themeMapping.theme_name,
    description: themeMapping.context,
    category: themeMapping.theme_category,
    media_types: ['ANIME'], // We know this is from anime
    examples: [],
    cross_media_mappings: {},
    llm_context: themeMapping.context
  }
  showThemeModal.value = true
}

function viewThemeDetails(themeMapping: ThemeMapping) {
  selectTheme(themeMapping)
}

function closeThemeModal() {
  showThemeModal.value = false
  selectedTheme.value = null
}

function getMappingTypeBadgeClass(mappingType: string): string {
  const classes = {
    PRIMARY: 'bg-green-100 text-green-800',
    SECONDARY: 'bg-yellow-100 text-yellow-800',
    TERTIARY: 'bg-gray-100 text-gray-800'
  }
  return classes[mappingType] || 'bg-gray-100 text-gray-800'
}

function getThemeCountByCategory(category: string): number {
  return themes.value.filter(t => t.theme_category === category).length
}

function formatCategory(category: string): string {
  return category.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

// Lifecycle
onMounted(() => {
  loadExistingThemes()
})
</script>

<style scoped>
.anime-theme-display {
  /* Custom styles if needed */
}
</style>
