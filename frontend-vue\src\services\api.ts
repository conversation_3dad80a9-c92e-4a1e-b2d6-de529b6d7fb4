import axios from 'axios'
import type {
  Theme,
  ThemeSearchQuery,
  ThemeSearchResponse,
  MediaAnalysisRequest,
  MediaAnalysisResponse,
  MCPCapabilities,
  AnimeSearchQuery,
  AnimeSearchResponse,
  AnimeSearchResult,
} from '@/types/theme'

const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

export const themeApi = {
  // MCP endpoints
  async getCapabilities(): Promise<MCPCapabilities> {
    const response = await api.get('/mcp/capabilities')
    return response.data
  },

  async getCategories(): Promise<any> {
    const response = await api.get('/mcp/resources/categories')
    return response.data
  },

  async searchThemes(query: ThemeSearchQuery): Promise<ThemeSearchResponse> {
    const response = await api.post('/mcp/tools/search-themes', query)
    return response.data
  },

  async analyzeMedia(request: MediaAnalysisRequest): Promise<MediaAnalysisResponse> {
    const response = await api.post('/mcp/tools/analyze-media', request)
    return response.data
  },

  async getThemeRelationships(themeId: string): Promise<any> {
    const response = await api.post('/mcp/tools/get-theme-relationships', {
      theme_id: themeId,
    })
    return response.data
  },

  // Traditional REST endpoints (if needed)
  async getAllThemes(): Promise<Theme[]> {
    const response = await api.get('/themes')
    return response.data
  },

  async getTheme(id: string): Promise<Theme> {
    const response = await api.get(`/themes/${id}`)
    return response.data
  },

  async createTheme(theme: Omit<Theme, 'id'>): Promise<Theme> {
    const response = await api.post('/themes', theme)
    return response.data
  },

  async updateTheme(id: string, theme: Partial<Theme>): Promise<Theme> {
    const response = await api.put(`/themes/${id}`, theme)
    return response.data
  },

  async deleteTheme(id: string): Promise<void> {
    await api.delete(`/themes/${id}`)
  },
}

export const animeApi = {
  // Anime/Story search endpoints
  async searchAnime(query: AnimeSearchQuery): Promise<AnimeSearchResponse> {
    const params = new URLSearchParams()
    params.append('query', query.query)
    if (query.page) params.append('page', query.page.toString())
    if (query.per_page) params.append('per_page', query.per_page.toString())
    if (query.media_type) params.append('media_type', query.media_type)
    if (query.sort) params.append('sort', query.sort)

    const url = `/stories/search?${params.toString()}`
    const response = await api.get(url)
    return response.data
  },

  async getAnime(id: string): Promise<AnimeSearchResult> {
    const response = await api.get(`/stories/${id}`)
    return response.data
  },

  async getAnimeMetadata(id: string): Promise<any> {
    const response = await api.get(`/stories/${id}/metadata`)
    return response.data
  },
}

// Theme mapping API
export const themeMappingApi = {
  // Get all themes from database
  async getAllThemes() {
    const response = await api.get('/themes')
    return response.data
  },
  // Map anime metadata to themes
  async mapAnimeThemes(animeMetadata: {
    title_romaji: string
    title_english?: string
    genres: string[]
    tags: Array<{ name: string; rank?: number }>
    synopsis?: string
  }) {
    const response = await api.post('/theme-mapping/anime/map-themes', animeMetadata)
    return response.data
  },

  // Get existing theme mappings for an anime
  async getAnimeThemes(animeId: string) {
    const response = await api.get(`/theme-mapping/anime/${animeId}/themes`)
    return response.data
  },

  // Create theme mappings in database
  async createAnimeThemeMappings(animeId: string, animeMetadata: {
    title_romaji: string
    title_english?: string
    genres: string[]
    tags: Array<{ name: string; rank?: number }>
    synopsis?: string
  }) {
    const response = await api.post(`/theme-mapping/anime/${animeId}/create-mappings`, animeMetadata)
    // The backend returns { anime_id, created_mappings, mappings }, we want the mappings array
    return response.data.mappings || []
  },

  // Get mapping statistics
  async getMappingStats() {
    const response = await api.get('/theme-mapping/stats')
    return response.data
  },

  // Get mapper information
  async getMapperInfo() {
    const response = await api.get('/theme-mapping/mapper/info')
    return response.data
  },

  // Refresh anime mappings
  async refreshAnimeThemeMappings(animeId: string, animeMetadata: {
    title_romaji: string
    title_english?: string
    genres: string[]
    tags: Array<{ name: string; rank?: number }>
    synopsis?: string
  }) {
    const response = await api.delete(`/theme-mapping/anime/${animeId}/themes`, {
      data: animeMetadata
    })
    // The backend returns { anime_id, created_mappings, mappings }, we want the mappings array
    return response.data.mappings || []
  }
}

export default api
