"""
Theme mapping service that integrates cross-media anime mapping with the database.
Handles creating and managing theme mappings for anime.
"""
import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from app.db.neo4j_session import driver
from app.services.mapping.cross_media_anime_mapper import CrossMediaAnimeMapper
# from app.crud.neo4j.theme import ThemeCRUD  # Avoiding circular import for now

logger = logging.getLogger(__name__)

class ThemeMappingService:
    """Service for creating and managing theme mappings for anime."""
    
    def __init__(self):
        self.anime_mapper = CrossMediaAnimeMapper()
        # self.theme_crud = ThemeCRUD()  # Avoiding circular import for now
    
    async def create_anime_theme_mappings(self, anime_id: str, anime_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create theme mappings for an anime based on its metadata.
        
        Args:
            anime_id: The anime's ID in our system
            anime_metadata: Dictionary containing AniList anime data
            
        Returns:
            List of created theme mapping records
        """
        try:
            # Get theme mappings from the mapper
            theme_mappings = await self.anime_mapper.map_anime_to_themes(anime_metadata)
            
            if not theme_mappings:
                logger.warning(f"No theme mappings found for anime {anime_id}")
                return []
            
            # Create theme mappings in the database
            created_mappings = []
            async with driver.session() as session:
                for mapping in theme_mappings:
                    theme = mapping["theme"]
                    
                    # Create the theme mapping relationship
                    mapping_record = await self._create_theme_mapping_relationship(
                        session=session,
                        source_id=anime_id,
                        source_type="Story",
                        theme_id=theme["id"],
                        mapping_strength=mapping["mapping_strength"],
                        mapping_type=mapping["mapping_type"],
                        context=mapping["context"],
                        source=mapping["source"]
                    )
                    
                    if mapping_record:
                        created_mappings.append(mapping_record)
            
            logger.info(f"Created {len(created_mappings)} theme mappings for anime {anime_id}")
            return created_mappings
            
        except Exception as e:
            logger.error(f"Error creating theme mappings for anime {anime_id}: {str(e)}")
            raise
    
    async def _create_theme_mapping_relationship(self, session, source_id: str, source_type: str, 
                                               theme_id: str, mapping_strength: float, 
                                               mapping_type: str, context: str, source: str) -> Optional[Dict[str, Any]]:
        """Create a theme mapping relationship in Neo4j."""
        try:
            mapping_id = f"mapping_{uuid.uuid4()}"
            now = datetime.now().isoformat()
            
            query = """
            MATCH (s:Story {id: $source_id})
            MATCH (t:Theme {id: $theme_id})
            MERGE (s)-[r:HAS_THEME {
                id: $mapping_id,
                mapping_strength: $mapping_strength,
                mapping_type: $mapping_type,
                context: $context,
                source: $source,
                created_at: $created_at,
                updated_at: $updated_at,
                needs_review: false
            }]->(t)
            RETURN s, r, t
            """
            
            result = await session.run(query, {
                "source_id": source_id,
                "theme_id": theme_id,
                "mapping_id": mapping_id,
                "mapping_strength": mapping_strength,
                "mapping_type": mapping_type,
                "context": context,
                "source": source,
                "created_at": now,
                "updated_at": now
            })
            
            record = await result.single()
            if record:
                return {
                    "id": mapping_id,
                    "source": dict(record["s"]),
                    "theme": dict(record["t"]),
                    "relationship": dict(record["r"])
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error creating theme mapping relationship: {str(e)}")
            return None
    
    async def get_anime_theme_mappings(self, anime_id: str) -> List[Dict[str, Any]]:
        """Get all theme mappings for an anime."""
        async with driver.session() as session:
            query = """
            MATCH (s:Story {id: $anime_id})-[r:HAS_THEME]->(t:Theme)
            RETURN s, r, t
            ORDER BY r.mapping_strength DESC
            """
            
            result = await session.run(query, {"anime_id": anime_id})
            mappings = []
            
            async for record in result:
                mappings.append({
                    "source": dict(record["s"]),
                    "theme": dict(record["t"]),
                    "relationship": dict(record["r"])
                })
            
            return mappings
    
    async def update_theme_mapping(self, mapping_id: str, updates: Dict[str, Any]) -> bool:
        """Update a theme mapping."""
        async with driver.session() as session:
            # Build update query dynamically
            set_clauses = []
            params = {"mapping_id": mapping_id, "updated_at": datetime.now().isoformat()}
            
            for key, value in updates.items():
                if key not in ["id", "created_at"]:  # Don't allow updating these fields
                    set_clauses.append(f"r.{key} = ${key}")
                    params[key] = value
            
            if not set_clauses:
                return False
            
            query = f"""
            MATCH ()-[r:HAS_THEME {{id: $mapping_id}}]->()
            SET {', '.join(set_clauses)}, r.updated_at = $updated_at
            RETURN r
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            return record is not None
    
    async def delete_theme_mapping(self, mapping_id: str) -> bool:
        """Delete a theme mapping."""
        async with driver.session() as session:
            query = """
            MATCH ()-[r:HAS_THEME {id: $mapping_id}]->()
            DELETE r
            RETURN count(r) as deleted_count
            """
            
            result = await session.run(query, {"mapping_id": mapping_id})
            record = await result.single()
            
            return record["deleted_count"] > 0
    
    async def get_mapping_statistics(self) -> Dict[str, Any]:
        """Get statistics about theme mappings."""
        async with driver.session() as session:
            # Count total mappings
            result = await session.run("MATCH ()-[r:HAS_THEME]->() RETURN count(r) as total")
            record = await result.single()
            total_mappings = record["total"]
            
            # Count mappings by type
            result = await session.run("""
                MATCH ()-[r:HAS_THEME]->()
                RETURN r.mapping_type as type, count(r) as count
                ORDER BY count DESC
            """)
            mappings_by_type = {}
            async for record in result:
                mappings_by_type[record["type"]] = record["count"]
            
            # Count anime with mappings
            result = await session.run("""
                MATCH (s:Story)-[r:HAS_THEME]->()
                RETURN count(DISTINCT s) as anime_with_mappings
            """)
            record = await result.single()
            anime_with_mappings = record["anime_with_mappings"]
            
            # Average mappings per anime
            avg_mappings = total_mappings / anime_with_mappings if anime_with_mappings > 0 else 0
            
            # Get mapper statistics
            mapper_stats = await self.anime_mapper.get_mapping_stats()
            
            return {
                "total_mappings": total_mappings,
                "mappings_by_type": mappings_by_type,
                "anime_with_mappings": anime_with_mappings,
                "average_mappings_per_anime": round(avg_mappings, 2),
                "mapper_stats": mapper_stats
            }
    
    async def refresh_anime_mappings(self, anime_id: str, anime_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Refresh theme mappings for an anime by deleting old ones and creating new ones."""
        async with driver.session() as session:
            # Delete existing mappings
            await session.run("""
                MATCH (s:Story {id: $anime_id})-[r:HAS_THEME]->()
                DELETE r
            """, {"anime_id": anime_id})
            
            logger.info(f"Deleted existing theme mappings for anime {anime_id}")
        
        # Create new mappings
        return await self.create_anime_theme_mappings(anime_id, anime_metadata)
