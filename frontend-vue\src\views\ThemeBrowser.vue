<template>
  <div class="theme-browser-page">
    <!-- <PERSON>er -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h1 class="text-3xl font-bold leading-tight text-gray-900">
            Theme Browser
          </h1>
          <p class="mt-2 text-lg text-gray-600">
            Explore cross-media themes and discover connections between anime, TV shows, books, and music.
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <router-link
            to="/admin/themes"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Admin Tools
          </router-link>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div v-if="mappingStats" class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Themes</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ mappingStats.total_themes_in_db }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Mappings</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ mappingStats.total_mappings }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h3a1 1 0 011 1v14a1 1 0 01-1 1H4a1 1 0 01-1-1V5a1 1 0 011-1h3z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Anime with Themes</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ mappingStats.anime_with_mappings }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Avg Themes/Anime</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ mappingStats.average_mappings_per_anime.toFixed(1) }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Theme Browser Component -->
    <ThemeBrowser
      :initial-category="route.query.category as string"
      :initial-media-type="route.query.media as string"
      @theme-selected="handleThemeSelected"
      @category-changed="handleCategoryChanged"
    />

    <!-- Demo Section -->
    <div class="mt-12 bg-gray-50 rounded-lg p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Try Theme Mapping</h2>
      <p class="text-gray-600 mb-4">
        Test the theme mapping system with sample anime data to see how themes are automatically detected.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          v-for="sample in sampleAnime"
          :key="sample.title"
          @click="testThemeMapping(sample)"
          :disabled="testingMapping"
          class="text-left p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <h3 class="font-medium text-gray-900">{{ sample.title }}</h3>
          <p class="text-sm text-gray-600 mt-1">{{ sample.genres.join(', ') }}</p>
          <div class="flex items-center mt-2 text-xs text-primary-600">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Test Mapping
          </div>
        </button>
      </div>

      <!-- Mapping Results -->
      <div v-if="mappingResults.length > 0" class="mt-6 border-t border-gray-200 pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Mapping Results</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="result in mappingResults"
            :key="result.theme_id"
            class="bg-white p-4 rounded-lg border border-gray-200"
          >
            <div class="flex items-start justify-between mb-2">
              <h4 class="font-medium text-gray-900">{{ result.theme_name }}</h4>
              <span class="text-xs px-2 py-1 rounded-full"
                    :class="getMappingTypeBadgeClass(result.mapping_type)">
                {{ result.mapping_type }}
              </span>
            </div>
            <p class="text-sm text-gray-600 mb-2">{{ result.context }}</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>{{ result.theme_category }}</span>
              <span>{{ Math.round(result.mapping_strength * 100) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { themeMappingApi } from '@/services/api'
import type { Theme, ThemeMapping, MappingStats } from '@/types/theme'
import ThemeBrowser from '@/components/ThemeBrowser.vue'

const route = useRoute()
const router = useRouter()

// State
const mappingStats = ref<MappingStats | null>(null)
const mappingResults = ref<ThemeMapping[]>([])
const testingMapping = ref(false)

// Sample anime for testing
const sampleAnime = [
  {
    title: 'Your Name',
    title_romaji: 'Kimi no Na wa',
    genres: ['Romance', 'Drama', 'Slice of Life'],
    tags: [
      { name: 'Coming of Age', rank: 85 },
      { name: 'School', rank: 70 },
      { name: 'Friendship', rank: 60 }
    ],
    synopsis: 'A romantic drama about two teenagers who mysteriously swap bodies.'
  },
  {
    title: 'Attack on Titan',
    title_romaji: 'Shingeki no Kyojin',
    genres: ['Action', 'Drama', 'Horror'],
    tags: [
      { name: 'Military', rank: 90 },
      { name: 'Tragedy', rank: 85 },
      { name: 'Gore', rank: 80 }
    ],
    synopsis: 'Humanity fights for survival against giant humanoid Titans.'
  },
  {
    title: 'My Hero Academia',
    title_romaji: 'Boku no Hero Academia',
    genres: ['Action', 'Comedy', 'School'],
    tags: [
      { name: 'School', rank: 95 },
      { name: 'Friendship', rank: 80 },
      { name: 'Team', rank: 75 }
    ],
    synopsis: 'Students train to become professional superheroes.'
  }
]

// Methods
async function loadMappingStats() {
  try {
    mappingStats.value = await themeMappingApi.getMappingStats()
  } catch (error) {
    console.error('Failed to load mapping stats:', error)
  }
}

async function testThemeMapping(anime: any) {
  try {
    testingMapping.value = true
    mappingResults.value = await themeMappingApi.mapAnimeThemes(anime)
  } catch (error) {
    console.error('Failed to test theme mapping:', error)
  } finally {
    testingMapping.value = false
  }
}

function handleThemeSelected(theme: Theme) {
  console.log('Theme selected:', theme)
  // Could navigate to theme detail page or show additional info
}

function handleCategoryChanged(category: string) {
  // Update URL query params to maintain state
  router.push({
    query: { ...route.query, category }
  })
}

function getMappingTypeBadgeClass(mappingType: string): string {
  const classes = {
    PRIMARY: 'bg-green-100 text-green-800',
    SECONDARY: 'bg-yellow-100 text-yellow-800',
    TERTIARY: 'bg-gray-100 text-gray-800'
  }
  return classes[mappingType] || 'bg-gray-100 text-gray-800'
}

// Lifecycle
onMounted(() => {
  loadMappingStats()
})
</script>

<style scoped>
.theme-browser-page {
  /* Custom styles if needed */
}
</style>
